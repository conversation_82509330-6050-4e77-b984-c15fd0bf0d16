﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{AC6EB101-399F-43A9-93CD-E4CAC537FC45}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SQLServerDAL</RootNamespace>
    <AssemblyName>SQLServerDAL</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DalBb1.cs" />
    <Compile Include="DalBb2.cs" />
    <Compile Include="DalDd1.cs" />
    <Compile Include="DalDd2.cs" />
    <Compile Include="DalKc_Pd1.cs" />
    <Compile Include="DalSysMenu1.cs" />
    <Compile Include="DalSysMenu2.cs" />
    <Compile Include="DalSysModule.cs" />
    <Compile Include="DalSysModuleAuth.cs" />
    <Compile Include="DalSysPara.cs" />
    <Compile Include="DalSysRole.cs" />
    <Compile Include="DalSysRoleAuth.cs" />
    <Compile Include="DalSysRoleModule.cs" />
    <Compile Include="DalSysRpt.cs" />
    <Compile Include="DalSysRpt_Class.cs" />
    <Compile Include="DalSysRpt_Class_Level.cs" />
    <Compile Include="DalYk_Ck1.cs" />
    <Compile Include="DalYk_Rk1.cs" />
    <Compile Include="DalYk_Rk2.cs" />
    <Compile Include="DalYp_Bhg.cs" />
    <Compile Include="DalZd_Blfy.cs" />
    <Compile Include="DalZd_Cctj.cs" />
    <Compile Include="DalZd_Cs.cs" />
    <Compile Include="DalZd_Czy.cs" />
    <Compile Include="DalZd_Hj.cs" />
    <Compile Include="DalZd_Hy.cs" />
    <Compile Include="DalZd_Jb.cs" />
    <Compile Include="DalZd_Jx.cs" />
    <Compile Include="DalZd_Kh.cs" />
    <Compile Include="DalZd_Kh_Ywy.cs" />
    <Compile Include="DalZd_KjDw.cs" />
    <Compile Include="DalZd_Pay.cs" />
    <Compile Include="DalZd_PxJh.cs" />
    <Compile Include="DalZd_PxJl.cs" />
    <Compile Include="DalZd_SbJc.cs" />
    <Compile Include="DalZd_Sfys.cs" />
    <Compile Include="DalZd_TjJh.cs" />
    <Compile Include="DalZd_TjJl.cs" />
    <Compile Include="DalZd_Wsd.cs" />
    <Compile Include="DalZd_Xq.cs" />
    <Compile Include="DalZd_Yd.cs" />
    <Compile Include="DalZd_YdJsr.cs" />
    <Compile Include="DalZd_Yd_Mdzy.cs" />
    <Compile Include="DalZd_Yd_Qtry.cs" />
    <Compile Include="DalZd_Yd_Xkzbg.cs" />
    <Compile Include="DalZd_Yd_ZlFzr.cs" />
    <Compile Include="DalZd_Yh.cs" />
    <Compile Include="DalZd_Ylqx.cs" />
    <Compile Include="DalZd_Yp1.cs" />
    <Compile Include="DalZd_Yp2.cs" />
    <Compile Include="DalZd_Yp2_Ph.cs" />
    <Compile Include="DalZd_Yp2_Ts.cs" />
    <Compile Include="DalZd_Yp3.cs" />
    <Compile Include="DalZd_Yp4.cs" />
    <Compile Include="DalZd_ZdYh.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\DbProviderFactory\DbProviderFactory.csproj">
      <Project>{FDF5D6D6-D281-4884-A81A-D0C49C2F3BC7}</Project>
      <Name>DbProviderFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\DBUtility\DBUtility.csproj">
      <Project>{9aa4cca5-b6d1-4719-b1af-880910bbc87e}</Project>
      <Name>DBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDAL\IDAL.csproj">
      <Project>{8dee3ca3-dde2-40b7-8e77-bc5d0fce94c1}</Project>
      <Name>IDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDBUtility\IDBUtility.csproj">
      <Project>{ccbb5cb6-0871-4d97-9eb3-a68b44cb7e86}</Project>
      <Name>IDBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBModel\YBModel.csproj">
      <Project>{3cb4dc25-92a0-49f4-a946-d9988b409c7c}</Project>
      <Name>YBModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisVar\ZTHisVar.csproj">
      <Project>{26bf0cc0-ae2a-459a-b41b-73f691f5299d}</Project>
      <Name>ZTHisVar</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>