BEGIN TRANSACTION
GO

IF COL_LENGTH('dbo.Yk_Ck1', 'Ck_Ok') IS NULL
BEGIN
	ALTER TABLE dbo.Yk_Ck1 ADD Ck_Ok VARCHAR(50) DEFAULT '未结算'
	PRINT '已成功添加 Yk_Ck1.Ck_Ok 列'
END
ELSE
BEGIN
	PRINT 'Yk_Ck1.Ck_Ok 列已存在，跳过创建'
END
GO


/****** Object:  Table [dbo].[Yk_Ck2]    Script Date: 2025-08-01 11:29:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Yk_Ck2]
(
	[Ck_Id] [int] NOT NULL,
	[Ck_Code] [char](9) NOT NULL,
	[Yp_Code] [char](11) NULL,
	[Ck_Sl] [numeric](12, 4) NULL,
	[Ck_Dj] [numeric](18, 6) NULL,
	[Ck_Money] [numeric](18, 4) NULL,
	[Ck_Memo] [varchar](50) NULL,
	CONSTRAINT [PK_Yk_Ck2_1] PRIMARY KEY CLUSTERED 
(
	[Ck_Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Yk_CkDrugtracinfo]    Script Date: 2025-08-01 11:29:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Yk_CkDrugtracinfo]
(
	[Ck_Id] [int] NOT NULL,
	[Ck_Code] [char](9) NOT NULL,
	[drug_trac_codg] [varchar](100) NOT NULL,
	CONSTRAINT [PK_Yk_CkDrugtracinfo] PRIMARY KEY CLUSTERED 
(
	[Ck_Id] ASC,
	[drug_trac_codg] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[Yk_Ck2] ADD  CONSTRAINT [DF_Table_1_Rk_Sl]  DEFAULT ((0)) FOR [Ck_Sl]
GO
ALTER TABLE [dbo].[Yk_Ck2] ADD  CONSTRAINT [DF_Table_1_Rk_Dj]  DEFAULT ((0)) FOR [Ck_Dj]
GO
ALTER TABLE [dbo].[Yk_Ck2] ADD  CONSTRAINT [DF_Table_1_Rk_Money]  DEFAULT ((0)) FOR [Ck_Money]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Sl'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_单价' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Dj'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_金额' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Money'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Memo'
GO

-- 添加外键关系
ALTER TABLE [dbo].[Yk_Ck2] ADD CONSTRAINT [FK_Yk_Ck2_Yk_Ck1] FOREIGN KEY([Ck_Code]) REFERENCES [dbo].[Yk_Ck1] ([Ck_Code])
GO
ALTER TABLE [dbo].[Yk_CkDrugtracinfo] ADD CONSTRAINT [FK_Yk_CkDrugtracinfo_Yk_Ck2] FOREIGN KEY([Ck_Id]) REFERENCES [dbo].[Yk_Ck2] ([Ck_Id])
GO
ALTER TABLE [dbo].[Yk_CkDrugtracinfo] ADD CONSTRAINT [FK_Yk_CkDrugtracinfo_Yk_Ck1] FOREIGN KEY([Ck_Code]) REFERENCES [dbo].[Yk_Ck1] ([Ck_Code])
GO

COMMIT TRANSACTION
PRINT '数据库更新成功完成！'
GO
