﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common.BaseForm;

namespace YdTraceCode
{
    public partial class AddRkDj : BaseChild
    {
        public decimal YpCgj = 0;
        public string Mx_Code = "";
        public string Dw = "";
        public AddRkDj(string Mx_Code, string Dw)
        {
            InitializeComponent();
            this.Mx_Code = Mx_Code;
            this.Dw = Dw;
        }

        private void AddRkDj_Load(object sender, EventArgs e)
        {
            lblDw1.Text = Dw;

            panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);

            BLL.BllYk_Rk2 bllYkRk2 = new BLL.BllYk_Rk2();
            DataTable ypRkTable = bllYkRk2.GetFirstYpDjList(Mx_Code).Tables[0];
            if (ypRkTable.Rows.Count > 0)
            {
                NumCgj.Value = Convert.ToDouble(ypRkTable.Rows[0]["Rk_Dj"]);
            }
            else
            {
                NumCgj.Value = 0;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (CustomControl.Func.NotAllowEmpty(NumCgj)) return;
            if (Convert.ToDecimal(NumCgj.Value) < 0)
            {
                MessageBox.Show("采购单价不能为负数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumCgj.Select();
                return;
            }
            YpCgj = Convert.ToDecimal(NumCgj.Value);
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
