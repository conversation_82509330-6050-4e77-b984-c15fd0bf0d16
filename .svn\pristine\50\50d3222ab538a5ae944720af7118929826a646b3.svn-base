﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common.BaseForm;
using Sunny.UI;

namespace YdTraceCode
{
    public partial class TraceCodeCRK : BaseChild
    {

        private Common.Delegate.TransmitTxt _transmitTxt;
        public Common.Delegate.TransmitTxt MyTransmitTxt
        {
            get { return _transmitTxt; }
            set
            {
                _transmitTxt = value;
            }

        }
        private string _lb;
        private DataTable _dt;
        private DataTable _tracTable;
        private string _rk_code;
        private string _Kh_Name;

        // 添加Timer来检测输入结束
        private Timer inputTimer;

        public TraceCodeCRK(string lb, DataTable dt, DataTable tracTable, string rk_code, string Kh_Name)
        {
            InitializeComponent();
            _lb = lb;
            _dt = dt;
            _tracTable = tracTable;
            _rk_code = rk_code;
            _Kh_Name = Kh_Name;

            // 初始化定时器
            InitializeTimer();
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            inputTimer = new Timer();
            inputTimer.Interval = 1000; // 默认2秒，实际从界面控件获取
            inputTimer.Tick += InputTimer_Tick;
        }

        /// <summary>
        /// 延迟时间数值改变事件
        /// </summary>
        private void numDelaySeconds_ValueChanged(object sender, EventArgs e)
        {
            // 更新定时器间隔（转换为毫秒）
            if (inputTimer != null)
            {
                inputTimer.Interval = (int)(Common.ConvertObject.ObjToInt(numDelaySeconds.Value) * 1000);
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("Settings", "TraceCodeCRKNumDelaySeconds", numDelaySeconds.Value.ToString());
            }
        }

        /// <summary>
        /// 定时器触发事件 - 检测到输入结束后自动处理批量追溯码
        /// </summary>
        private void InputTimer_Tick(object sender, EventArgs e)
        {
            inputTimer.Stop();

            // 只有在批量扫码模式且有待处理的追溯码时才自动处理
            if (chkPlsm.Checked && listBox2.Items.Count > 0)
            {
                PlAddTraceCode();
            }
        }

        private void TraceCodeCRK_Load(object sender, EventArgs e)
        {
            this.Text = _lb;
            TxtTraceCode.Text = "";
            TxtTraceCode.Focus();
            foreach (DataRow row in _tracTable.Rows)
            {
                listBox1.Items.Add(row["drug_trac_codg"].ToString());
            }
            string plsm = Common.WinFormVar.Var.IniFileHelper.IniReadValue("Settings", "TraceCodeCRKPlsm") ?? string.Empty;
            chkPlsm.Checked = !string.IsNullOrEmpty(plsm) && bool.Parse(plsm);
            string numDelaySecondsStr = Common.WinFormVar.Var.IniFileHelper.IniReadValue("Settings", "TraceCodeCRKNumDelaySeconds") ?? string.Empty;
            numDelaySeconds.Value = string.IsNullOrEmpty(numDelaySecondsStr) ? 2 : int.Parse(numDelaySecondsStr);
            numDelaySeconds.CustomFormat = "0";
            label1.Text = $"已扫码 {listBox1.Items.Count} 条";

            // 设置初始延迟时间
            numDelaySeconds_ValueChanged(null, null);
            PlsmChange();
        }

        private void TxtTraceCode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                if (!string.IsNullOrWhiteSpace(TxtTraceCode.Text))
                {
                    if (!chkPlsm.Checked)
                    {
                        AddTraceCode(TxtTraceCode.Text);
                    }
                    else
                    {
                        string traceCode = TxtTraceCode.Text.Trim();

                        // 检查是否已存在，避免重复添加
                        if (!listBox2.Items.Contains(traceCode))
                        {
                            listBox2.Items.Add(traceCode);
                        }

                        // 重置定时器 - 每次输入后重新计时
                        inputTimer.Stop();
                        inputTimer.Start();

                        // 清空输入框
                        TxtTraceCode.Text = "";
                        TxtTraceCode.Focus();
                    }
                }
            }
        }
        private void chkPlsm_CheckedChanged(object sender, EventArgs e)
        {
            PlsmChange();
            Common.WinFormVar.Var.IniFileHelper.IniWriteValue("Settings", "TraceCodeCRKPlsm", chkPlsm.Checked.ToString());
        }
        #region 方法

        private void PlAddTraceCode()
        {
            foreach (string traceCode in listBox2.Items)
            {
                AddTraceCode(traceCode);
            }
            listBox2.Items.Clear();
        }

        private void AddTraceCode(string traceCode)
        {
            if (traceCode.Length != 20)
            {
                if (MessageBox.Show("追溯码长度不是20位，是否继续？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.No)
                {
                    TxtTraceCode.Text = "";
                    TxtTraceCode.Focus();
                    return;
                }
            }
            UIWaitFormService.ShowWaitForm("正在调用码上放心接口，请稍后...");
            string msg = "";
            bool result = false;
            List<string> listTraceCode = new List<string>();
            switch (_lb)
            {
                case "药品入库":
                    result = TraceCodeJxcFunc.YkRkByTraceCode(true, _dt, _tracTable, traceCode, _rk_code, _Kh_Name, ref msg, out listTraceCode);
                    break;
                default:
                    break;
            }
            if (!result)
            {
                UIWaitFormService.HideWaitForm();
                MessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            else
            {
                listBox1.Items.AddRange(listTraceCode.ToArray());
                label1.Text = $"已扫码 {listBox1.Items.Count} 条";
            }
            this.MyTransmitTxt.OnSetText("最后");
            UIWaitFormService.HideWaitForm();
            TxtTraceCode.Text = "";
            TxtTraceCode.Focus();
        }

        private void PlsmChange()
        {
            if (chkPlsm.Checked)
            {
                numDelaySeconds.Enabled = true;
                listBox2.Enabled = true;
            }
            else
            {
                numDelaySeconds.Enabled = false;
                listBox2.Enabled = false;
            }
        }
        #endregion


    }
}
