﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common;
using Common.BaseForm;
using Model;

namespace YdTraceCode
{
    public partial class SelectYp : BaseChild
    {
        private CurrencyManager MyCm;
        private List<MdlZd_Yp2> list;
        public MdlZd_Yp2 MdlZdMlYp2;
        public string Mx_Gyzz;
        public SelectYp(List<MdlZd_Yp2> list, string Mx_Gyzz)
        {
            InitializeComponent();
            this.list = list;
            this.Mx_Gyzz = Mx_Gyzz;
        }

        private void SelectYp_Load(object sender, EventArgs e)
        {
            lblTips.Text = $"批准文号：{Mx_Gyzz}有多个药品记录，请双击记录选择药品";
            myGrid1.Init_Grid();
            myGrid1.Init_Column("药品名称", "Yp_Name", 240, "左", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 150, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 300, "左", "", false);
            myGrid1.Init_Column("制剂规格", "Yp_Zjgg", 180, "左", "", false);
            myGrid1.Init_Column("制剂单位", "Yp_Zjdw", 100, "左", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 100, "左", "", false);
            myGrid1.Init_Column("包装规格", "Yp_Bzgg", 100, "左", "", false);
            myGrid1.Init_Column("包装数量", "Yp_Bzzhb", 100, "左", "", false);

            myGrid1.DataTable = list;
            MyCm = (CurrencyManager)BindingContext[list, ""];
        }

        private void myGrid1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (myGrid1.RowCount == 0) return;
            MdlZdMlYp2 = ((MdlZd_Yp2)MyCm.List[myGrid1.Row]);
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
