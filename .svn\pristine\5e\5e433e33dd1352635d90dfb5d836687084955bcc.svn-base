﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_Yp2.cs
*
* 功 能： N/A
* 类 名： DalZd_Yp2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:26   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_Yp2
	/// </summary>
	public partial class DalZd_Yp2 : IDalZd_Yp2
	{
		public DalZd_Yp2()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Xl_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_Yp2");
			strSql.Append(" where Xl_Code=@Xl_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xl_Code", SqlDbType.Char,8)          };
			parameters[0].Value = Xl_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_Yp2 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_Yp2(");
			strSql.Append("Dl_Code,Xl_Code,Tm_Code,Yp_Pzwh,Yp_Name,Yp_Jc,Yp_Zjgg,Yp_Zjdw,Yp_Jx,Yp_Bzgg,Yp_Bzzhb,Yp_Scqy,Yp_Xsdj,Ts_Code,Yp_Otc,Yp_Lc,Yp_Memo,Xl_Count,Sc_Finish,Drug_Identification_Code)");
			strSql.Append(" values (");
			strSql.Append("@Dl_Code,@Xl_Code,@Tm_Code,@Yp_Pzwh,@Yp_Name,@Yp_Jc,@Yp_Zjgg,@Yp_Zjdw,@Yp_Jx,@Yp_Bzgg,@Yp_Bzzhb,@Yp_Scqy,@Yp_Xsdj,@Ts_Code,@Yp_Otc,@Yp_Lc,@Yp_Memo,@Xl_Count,@Sc_Finish,@Drug_Identification_Code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Dl_Code", SqlDbType.Char,2),
					new SqlParameter("@Xl_Code", SqlDbType.Char,8),
					new SqlParameter("@Tm_Code", SqlDbType.VarChar,13),
					new SqlParameter("@Yp_Pzwh", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Name", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Jc", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Zjgg", SqlDbType.VarChar,200),
					new SqlParameter("@Yp_Zjdw", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Jx", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Bzgg", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Bzzhb", SqlDbType.Int,4),
					new SqlParameter("@Yp_Scqy", SqlDbType.VarChar,200),
					new SqlParameter("@Yp_Xsdj", SqlDbType.Decimal,9),
					new SqlParameter("@Ts_Code", SqlDbType.Char,2),
					new SqlParameter("@Yp_Otc", SqlDbType.Bit,1),
					new SqlParameter("@Yp_Lc", SqlDbType.Bit,1),
					new SqlParameter("@Yp_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Xl_Count", SqlDbType.Int,4),
					new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Drug_Identification_Code", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Dl_Code;
			parameters[1].Value = model.Xl_Code;
			parameters[2].Value = model.Tm_Code;
			parameters[3].Value = model.Yp_Pzwh;
			parameters[4].Value = model.Yp_Name;
			parameters[5].Value = model.Yp_Jc;
			parameters[6].Value = model.Yp_Zjgg;
			parameters[7].Value = model.Yp_Zjdw;
			parameters[8].Value = model.Yp_Jx;
			parameters[9].Value = model.Yp_Bzgg;
			parameters[10].Value = model.Yp_Bzzhb;
			parameters[11].Value = model.Yp_Scqy;
			parameters[12].Value = model.Yp_Xsdj;
			parameters[13].Value = model.Ts_Code;
			parameters[14].Value = model.Yp_Otc;
			parameters[15].Value = model.Yp_Lc;
			parameters[16].Value = model.Yp_Memo;
			parameters[17].Value = model.Xl_Count;
			parameters[18].Value = model.Sc_Finish;
			parameters[19].Value = model.Drug_Identification_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_Yp2 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_Yp2 set ");
			strSql.Append("Dl_Code=@Dl_Code,");
			strSql.Append("Tm_Code=@Tm_Code,");
			strSql.Append("Yp_Pzwh=@Yp_Pzwh,");
			strSql.Append("Yp_Name=@Yp_Name,");
			strSql.Append("Yp_Jc=@Yp_Jc,");
			strSql.Append("Yp_Zjgg=@Yp_Zjgg,");
			strSql.Append("Yp_Zjdw=@Yp_Zjdw,");
			strSql.Append("Yp_Jx=@Yp_Jx,");
			strSql.Append("Yp_Bzgg=@Yp_Bzgg,");
			strSql.Append("Yp_Bzzhb=@Yp_Bzzhb,");
			strSql.Append("Yp_Scqy=@Yp_Scqy,");
			strSql.Append("Yp_Xsdj=@Yp_Xsdj,");
			strSql.Append("Ts_Code=@Ts_Code,");
			strSql.Append("Yp_Otc=@Yp_Otc,");
			strSql.Append("Yp_Lc=@Yp_Lc,");
			strSql.Append("Yp_Memo=@Yp_Memo,");
			strSql.Append("Xl_Count=@Xl_Count,");
			strSql.Append("Sc_Finish=@Sc_Finish,");
			strSql.Append("Drug_Identification_Code=@Drug_Identification_Code");
			strSql.Append(" where Xl_Code=@Xl_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dl_Code", SqlDbType.Char,2),
					new SqlParameter("@Tm_Code", SqlDbType.VarChar,13),
					new SqlParameter("@Yp_Pzwh", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Name", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Jc", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Zjgg", SqlDbType.VarChar,200),
					new SqlParameter("@Yp_Zjdw", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Jx", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Bzgg", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Bzzhb", SqlDbType.Int,4),
					new SqlParameter("@Yp_Scqy", SqlDbType.VarChar,200),
					new SqlParameter("@Yp_Xsdj", SqlDbType.Decimal,9),
					new SqlParameter("@Ts_Code", SqlDbType.Char,2),
					new SqlParameter("@Yp_Otc", SqlDbType.Bit,1),
					new SqlParameter("@Yp_Lc", SqlDbType.Bit,1),
					new SqlParameter("@Yp_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Xl_Count", SqlDbType.Int,4),
					new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Drug_Identification_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Xl_Code", SqlDbType.Char,8)};
			parameters[0].Value = model.Dl_Code;
			parameters[1].Value = model.Tm_Code;
			parameters[2].Value = model.Yp_Pzwh;
			parameters[3].Value = model.Yp_Name;
			parameters[4].Value = model.Yp_Jc;
			parameters[5].Value = model.Yp_Zjgg;
			parameters[6].Value = model.Yp_Zjdw;
			parameters[7].Value = model.Yp_Jx;
			parameters[8].Value = model.Yp_Bzgg;
			parameters[9].Value = model.Yp_Bzzhb;
			parameters[10].Value = model.Yp_Scqy;
			parameters[11].Value = model.Yp_Xsdj;
			parameters[12].Value = model.Ts_Code;
			parameters[13].Value = model.Yp_Otc;
			parameters[14].Value = model.Yp_Lc;
			parameters[15].Value = model.Yp_Memo;
			parameters[16].Value = model.Xl_Count;
			parameters[17].Value = model.Sc_Finish;
			parameters[18].Value = model.Drug_Identification_Code;
			parameters[19].Value = model.Xl_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Xl_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yp2 ");
			strSql.Append(" where Xl_Code=@Xl_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xl_Code", SqlDbType.Char,8)          };
			parameters[0].Value = Xl_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Xl_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yp2 ");
			strSql.Append(" where Xl_Code in (" + Xl_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yp2 GetModel(string Xl_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Dl_Code,Xl_Code,Tm_Code,Yp_Pzwh,Yp_Name,Yp_Jc,Yp_Zjgg,Yp_Zjdw,Yp_Jx,Yp_Bzgg,Yp_Bzzhb,Yp_Scqy,Yp_Xsdj,Ts_Code,Yp_Otc,Yp_Lc,Yp_Memo,Xl_Count,Sc_Finish,Drug_Identification_Code from Zd_Yp2 ");
			strSql.Append(" where Xl_Code=@Xl_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xl_Code", SqlDbType.Char,8)          };
			parameters[0].Value = Xl_Code;

			Model.MdlZd_Yp2 model = new Model.MdlZd_Yp2();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yp2 DataRowToModel(DataRow row)
		{
			Model.MdlZd_Yp2 model = new Model.MdlZd_Yp2();
			if (row != null)
			{
				if (row["Dl_Code"] != null)
				{
					model.Dl_Code = row["Dl_Code"].ToString();
				}
				if (row["Xl_Code"] != null)
				{
					model.Xl_Code = row["Xl_Code"].ToString();
				}
				if (row["Tm_Code"] != null)
				{
					model.Tm_Code = row["Tm_Code"].ToString();
				}
				if (row["Yp_Pzwh"] != null)
				{
					model.Yp_Pzwh = row["Yp_Pzwh"].ToString();
				}
				if (row["Yp_Name"] != null)
				{
					model.Yp_Name = row["Yp_Name"].ToString();
				}
				if (row["Yp_Jc"] != null)
				{
					model.Yp_Jc = row["Yp_Jc"].ToString();
				}
				if (row["Yp_Zjgg"] != null)
				{
					model.Yp_Zjgg = row["Yp_Zjgg"].ToString();
				}
				if (row["Yp_Zjdw"] != null)
				{
					model.Yp_Zjdw = row["Yp_Zjdw"].ToString();
				}
				if (row["Yp_Jx"] != null)
				{
					model.Yp_Jx = row["Yp_Jx"].ToString();
				}
				if (row["Yp_Bzgg"] != null)
				{
					model.Yp_Bzgg = row["Yp_Bzgg"].ToString();
				}
				if (row["Yp_Bzzhb"] != null && row["Yp_Bzzhb"].ToString() != "")
				{
					model.Yp_Bzzhb = int.Parse(row["Yp_Bzzhb"].ToString());
				}
				if (row["Yp_Scqy"] != null)
				{
					model.Yp_Scqy = row["Yp_Scqy"].ToString();
				}
				if (row["Yp_Xsdj"] != null && row["Yp_Xsdj"].ToString() != "")
				{
					model.Yp_Xsdj = decimal.Parse(row["Yp_Xsdj"].ToString());
				}
				if (row["Ts_Code"] != null)
				{
					model.Ts_Code = row["Ts_Code"].ToString();
				}
				if (row["Yp_Otc"] != null && row["Yp_Otc"].ToString() != "")
				{
					if ((row["Yp_Otc"].ToString() == "1") || (row["Yp_Otc"].ToString().ToLower() == "true"))
					{
						model.Yp_Otc = true;
					}
					else
					{
						model.Yp_Otc = false;
					}
				}
				if (row["Yp_Lc"] != null && row["Yp_Lc"].ToString() != "")
				{
					if ((row["Yp_Lc"].ToString() == "1") || (row["Yp_Lc"].ToString().ToLower() == "true"))
					{
						model.Yp_Lc = true;
					}
					else
					{
						model.Yp_Lc = false;
					}
				}
				if (row["Yp_Memo"] != null)
				{
					model.Yp_Memo = row["Yp_Memo"].ToString();
				}
				if (row["Xl_Count"] != null && row["Xl_Count"].ToString() != "")
				{
					model.Xl_Count = int.Parse(row["Xl_Count"].ToString());
				}
				if (row["Sc_Finish"] != null && row["Sc_Finish"].ToString() != "")
				{
					if ((row["Sc_Finish"].ToString() == "1") || (row["Sc_Finish"].ToString().ToLower() == "true"))
					{
						model.Sc_Finish = true;
					}
					else
					{
						model.Sc_Finish = false;
					}
				}
				if (row["Drug_Identification_Code"] != null)
				{
					model.Drug_Identification_Code = row["Drug_Identification_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Dl_Code,Xl_Code,Tm_Code,Yp_Pzwh,Yp_Name,Yp_Jc,Yp_Zjgg,Yp_Zjdw,Yp_Jx,Yp_Bzgg,Yp_Bzzhb,Yp_Scqy,Yp_Xsdj,Ts_Code,Yp_Otc,Yp_Lc,Yp_Memo,Xl_Count,Sc_Finish,Drug_Identification_Code ");
			strSql.Append(" FROM Zd_Yp2 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Dl_Code,Xl_Code,Tm_Code,Yp_Pzwh,Yp_Name,Yp_Jc,Yp_Zjgg,Yp_Zjdw,Yp_Jx,Yp_Bzgg,Yp_Bzzhb,Yp_Scqy,Yp_Xsdj,Ts_Code,Yp_Otc,Yp_Lc,Yp_Memo,Xl_Count,Sc_Finish,Drug_Identification_Code ");
			strSql.Append(" FROM Zd_Yp2 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Yp2 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Xl_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Yp2 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Yp2";
			parameters[1].Value = "Xl_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(Xl_Code) FROM Zd_Yp2 where LEN(Xl_Code)=" + length, length));
			return max;
		}

		#endregion  ExtensionMethod
	}
}

