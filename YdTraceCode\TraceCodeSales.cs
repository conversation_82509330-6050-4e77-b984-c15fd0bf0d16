﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics.Eventing.Reader;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using C1.Win.C1TrueDBGrid;
using Common;
using Common.BaseForm;
using Model;
using Sunny.UI;
using YBModel;
using ZTHisInsurance_Enum;
using ZTHisInsuranceAPI;

namespace YdTraceCode
{
    public partial class TraceCodeSales : BaseChild
    {
        private DataTable ypTable = new DataTable();
        private CurrencyManager _ypCm;
        private DataView _ypView;
        private DataRow _ypRow;
        private YBBLL.BllCountry_YB_SpXs_Drugtracinfo bllCountryYbSpXsDrugtracinfo = new YBBLL.BllCountry_YB_SpXs_Drugtracinfo();
        private YBBLL.BllCountry_YB_SpXsTh_Drugtracinfo bllCountryYbSpXsThDrugtracinfo = new YBBLL.BllCountry_YB_SpXsTh_Drugtracinfo();
        private YBBLL.BllCountry_YB_SpKc_Drugtracinfo bllCountryYbSpKcDrugtracinfo = new YBBLL.BllCountry_YB_SpKc_Drugtracinfo();
        private BLL.BllZd_Ml_Yp3 _bllZd_Ml_Yp3 = new BLL.BllZd_Ml_Yp3();
        private DataTable tracTable = new DataTable();
        private CurrencyManager _tracCm;
        private DataView _tracView;
        private string type;
        private string Field_Sl = "Mz_Sl";
        private string Field_Lb = "Mz_Lb";
        private string Field_Id = "Mz_Id";
        private string Field_Code = "Mz_Code";
        private string Field_Ex = "Mz";
        private bool _initFlag = false;
        public TraceCodeSales(DataRow ypRow, DataTable ypTable, DataTable tracTable, string type)
        {
            this._ypRow = ypRow;
            this.ypTable = ypTable;
            this.tracTable = tracTable;
            this.type = type;
            InitializeComponent();
        }

        private void TraceCodeDetail_Load(object sender, EventArgs e)
        {
            _initFlag = false;
            FieldInit();
            FormInit();
            _initFlag = true;
        }

        #region 函数

        private void FieldInit()
        {
            switch (type)
            {
                case "门诊收费":
                case "门诊发药":
                    Field_Sl = "Mz_Sl";
                    Field_Lb = "Mz_Lb";
                    Field_Id = "Mz_Id";
                    Field_Code = "Mz_Code";
                    Field_Ex = "MZ";
                    break;
                case "药房入库":
                    Field_Sl = "Rk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Rk_Code";
                    Field_Ex = "YFRK";
                    break;
                case "药库入库":
                    Field_Sl = "Rk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Rk_Code";
                    Field_Ex = "YKRK";
                    break;
                case "药库批发":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YKPF";
                    break;
                case "药房批发":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YFPF";
                    break;
                case "药库退库":
                    Field_Sl = "Tk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Tk_Code";
                    Field_Ex = "YKTK";
                    break;
                case "药房退库":
                    Field_Sl = "Tk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Tk_Code";
                    Field_Ex = "YFTK";
                    break;
                case "药库科室支领":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YKKS";
                    break;
                case "药房科室支领":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YFKS";
                    break;
                case "药库调拨药房":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YKYF";
                    break;
                case "药房退回药库":
                    Field_Sl = "Tk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Tk_Code";
                    Field_Ex = "YFYK";
                    break;
                case "住院发药":
                    Field_Sl = "Cf_Sl";
                    Field_Lb = "Cf_Lb";
                    Field_Id = "Cf_Id";
                    Field_Code = "Cf_Code";
                    Field_Ex = "ZY";
                    break;
            }
        }
        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("药品名称", "Yp_Name", 180, "左", "", false);
            myGrid1.Init_Column("数量", Field_Sl, 60, "右", "##0.###", false);
            myGrid1.Init_Column("追溯码", "traccnt", 60, "右", "##0.###", false);
            myGrid1.AllowSort = false;
            myGrid1.Splits[0].DisplayColumns["traccnt"].FetchStyle = true;
            _ypCm = (CurrencyManager)BindingContext[ypTable, ""];
            _ypView = (DataView)_ypCm.List;
            myGrid1.DataTable = ypTable;

            myGrid2.Init_Grid();
            myGrid2.Init_Column("选择", "IsCheck", 55, "中", "Check", true);
            myGrid2.Init_Column("药品追溯码", "drug_trac_codg", 150, "左", "", false);
            myGrid2.AllowSort = false;

            TxtTraceCode.Text = "";
            TxtTraceCode.Select();

            _tracCm = (CurrencyManager)BindingContext[tracTable, ""];
            _tracView = (DataView)_tracCm.List;
            myGrid2.DataTable = tracTable;

            myGrid1.Row = ypTable.Rows.IndexOf(_ypRow);
            DataInit(GenJxcCode(_ypRow));

            string para = Common.WinFormVar.Var.IniFileHelper.IniReadValue("Settings", "TraceCodeCheckLength");
            chkLength.Checked = bool.TryParse(para, out bool result) && result;
            para = Common.WinFormVar.Var.IniFileHelper.IniReadValue("Settings", "TraceCodeCheckRk");
            chkCheckRk.Checked = bool.TryParse(para, out result) && result;
            if (type == "药房入库" || type == "药库入库")
            {
                chkCheckRk.Enabled = false;
                chkCheckRk.Visible = false;
                lblTips.Visible = false;
            }
            else
            {
                chkCheckRk.Enabled = true;
                chkCheckRk.Visible = true;
                lblTips.Visible = true;
            }
        }

        private void DataInit(string jxc_code)
        {
            _tracView.RowFilter = $"jxc_code='{jxc_code}'";
        }

        private bool ScanTraceCode(string traceCode, DataRow ypRow)
        {
            if (string.IsNullOrEmpty(traceCode))
            {
                MessageBox.Show("追溯码不能为空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ypRow[Field_Lb] + "" != "西药" && ypRow[Field_Lb] + "" != "中成药" && ypRow[Field_Lb] + "" != "卫生材料")
            {
                MessageBox.Show("只有西药，中成药，卫生材料需要扫追溯码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                myGrid1.MoveNext();
                ypRow = (_ypView[myGrid1.Row]).Row;
                return false;
            }

            if (chkLength.Checked && traceCode.Length != 20)
            {
                MessageBox.Show("追溯码长度不为20位", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            //不验证入库，判断扫的追溯码和药品标识码是否一致
            // 验证追溯码和药品标识码是否一致
            bool isInboundType = (type == "药房入库" || type == "药库入库");
            bool shouldValidate = isInboundType || !chkCheckRk.Checked;

            if (shouldValidate && IsTraceCodeMismatch(traceCode))
            {
                string drugIdentificationCode = ypRow["Drug_Identification_Code"]?.ToString() ?? "";
                string message = $"追溯码【{traceCode}】不正确，和药品标识码【{drugIdentificationCode}】不一致，是否继续？\n\r如果确认追溯码正确，请在药品字典中修改药品标识码";

                if (MessageBox.Show(message, "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.No)
                {
                    return false;
                }
            }


            if (chkCheckRk.Checked)  // 验证入库追溯码
            {
                switch (type)
                {
                    case "门诊收费":
                    case "门诊发药":
                    case "住院发药":
                    case "药房批发":
                    case "药房退库":
                    case "药库退库":
                    case "药库批发":
                    case "药库科室支领":
                    case "药房科室支领":
                    case "药库调拨药房":
                    case "药房退回药库":
                        if (bllCountryYbSpKcDrugtracinfo.GetRecordCount($"drug_trac_codg='{traceCode}' And (Substring(jxc_code,1,4)='YFRK' Or Substring(jxc_code,1,4)='YKRK')") == 0)
                        {
                            MessageBox.Show($"追溯码【{traceCode}】未入库", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                            return false;
                        }
                        var listDrugTracCodg = bllCountryYbSpKcDrugtracinfo.GetModelList($"Drug_Trac_Codg='{traceCode}' And Substring(jxc_code,1,4) In ('YKRK','YFRK')");
                        MdlZd_Ml_Yp3 mdlZdMlYp3 = new MdlZd_Ml_Yp3();
                        List<MdlZd_Ml_Yp3> ListYp = new List<MdlZd_Ml_Yp3>();
                        DataRow SubItemRow = null;
                        string Xx_Code = "";
                        if (listDrugTracCodg.Count == 0)
                        {
                            MessageBox.Show($"追溯码【{traceCode}】没有入库，无法进行{type}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                            return false;
                        }
                        else
                        {
                            Xx_Code = listDrugTracCodg[0].jxc_code.Split('-')[1];
                            SubItemRow = ypTable.AsEnumerable().FirstOrDefault(p => p["Xx_Code"].ToString() == Xx_Code);
                            if (SubItemRow == null)
                            {
                                BllZd_Ml_Yp4 bllZdMlYp4 = new BllZd_Ml_Yp4();
                                var mdlZdMlYp4 = bllZdMlYp4.GetModel(Xx_Code);
                                MessageBox.Show($"追溯码【{traceCode}】不正确，未在{type}单中找到对应药品【{mdlZdMlYp4.Yp_Name}】，请检查后重新扫码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                                return false;
                            }
                            ypRow = SubItemRow;
                        }
                        break;
                }
            }
            switch (type)
            {
                case "门诊收费":
                case "门诊发药":
                case "住院发药":
                    if (!TraceCodeFunc.CheckTraceCode(traceCode, type, 1, ypRow["Xx_Code"].ToString(), out string msg))
                    {
                        MessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        return false;
                    }
                    break;
            }

            if (Math.Abs((decimal)ypRow[Field_Sl]) == tracTable.AsEnumerable().Where(p => p.Field<string>("jxc_code") == GenJxcCode(ypRow)).Count())
            {
                MessageBox.Show("追溯码数量已扫全", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            DataRow row = tracTable.NewRow();
            row["jxc_code"] = GenJxcCode(ypRow);
            row["drug_trac_codg"] = traceCode;
            tracTable.Rows.Add(row);
            tracTable.AcceptChanges();
            switch (type)
            {
                case "门诊收费":
                case "门诊发药":
                case "住院发药":
                    if (decimal.Parse(ypRow[Field_Sl] + "") > 0)
                    {
                        MdlCountry_YB_SpXs_Drugtracinfo mdl = new MdlCountry_YB_SpXs_Drugtracinfo();
                        mdl = Common.DataTableToList.ToModel<MdlCountry_YB_SpXs_Drugtracinfo>(row);
                        row["id"] = bllCountryYbSpXsDrugtracinfo.Add(mdl);
                    }
                    else
                    {
                        MdlCountry_YB_SpXsTh_Drugtracinfo mdl = new MdlCountry_YB_SpXsTh_Drugtracinfo();
                        mdl = Common.DataTableToList.ToModel<MdlCountry_YB_SpXsTh_Drugtracinfo>(row);
                        row["id"] = bllCountryYbSpXsThDrugtracinfo.Add(mdl);
                    }
                    if (traceCode != $"{Yb_Info.fixmedins_code.Substring(1, 2)}000000000000000000")
                    {
                        _bllZd_Ml_Yp3.UpdateDrug_Identification_Code(traceCode.Substring(0, 7), ypRow["Xx_Code"].ToString().Substring(0, 11));
                    }
                    break;
                case "药房入库":
                case "药库入库":
                case "药库批发":
                case "药房批发":
                case "药房退库":
                case "药库退库":
                case "药库科室支领":
                case "药房科室支领":
                case "药库调拨药房":
                case "药房退回药库":
                    {
                        MdlCountry_YB_SpKc_Drugtracinfo mdl = new MdlCountry_YB_SpKc_Drugtracinfo();
                        mdl = Common.DataTableToList.ToModel<MdlCountry_YB_SpKc_Drugtracinfo>(row);
                        row["id"] = bllCountryYbSpKcDrugtracinfo.Add(mdl);
                    }
                    break;
            }
            ypRow["traccnt"] = tracTable.AsEnumerable().Where(p => p.Field<string>("jxc_code") == GenJxcCode(ypRow) + "").Count();
            TxtTraceCode.Invoke(new Action(() =>
            {
                TxtTraceCode.Text = "";
                TxtTraceCode.Focus();
                TxtTraceCode.Select();
            }));

            if (!chkCheckRk.Checked)
            {
                if (Math.Abs((decimal)ypRow[Field_Sl]) == tracTable.AsEnumerable().Where(p => p.Field<string>("jxc_code") == GenJxcCode(ypRow) + "").Count())
                {
                    if (myGrid1.Row + 1 == myGrid1.RowCount)
                    {
                        return true;
                    }
                    else
                    {
                        myGrid1.MoveNext();
                        ypRow = (_ypView[myGrid1.Row]).Row;
                    }
                }
            }
            return true;
        }

        private string GenJxcCode(DataRow ypRow)
        {
            return Field_Ex + ypRow[Field_Code] + "-" + ypRow[Field_Id];
        }

        /// <summary>
        /// 检查追溯码和药品标识码是否不匹配
        /// </summary>
        /// <param name="traceCode">追溯码</param>
        /// <returns>如果不匹配返回true，否则返回false</returns>
        private bool IsTraceCodeMismatch(string traceCode)
        {
            string drugIdentificationCode = _ypRow["Drug_Identification_Code"]?.ToString() ?? "";
            string fixmedinsCode = Yb_Info.fixmedins_code ?? "";

            // 检查必要字段是否为空
            if (string.IsNullOrEmpty(drugIdentificationCode))
            {
                return false;
            }
            if (!string.IsNullOrEmpty(fixmedinsCode))
            {
                // 无追溯码情况
                string defaultTraceCode = $"{fixmedinsCode.Substring(1, 2)}000000000000000000";

                // 如果是默认追溯码，不需要验证
                if (traceCode == defaultTraceCode)
                {
                    return false;
                }
            }
            // 检查追溯码前7位是否与药品标识码匹配
            return drugIdentificationCode != traceCode.Substring(0, 7);
        }

        #endregion

        #region 事件

        private void TxtTraceCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (!StringExtension.IsNullOrEmpty(TxtTraceCode.Text))
                {
                    string traceCode = TxtTraceCode.Text.Trim();
                    TxtTraceCode.Text = "";
                    Task.Factory.StartNew(() =>
                    {
                        ScanTraceCode(traceCode, _ypRow);
                    });
                }
            }
        }
        private void CmdCopy_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {   //非拆零药品提示
            _ypRow = (_ypView[myGrid1.Row]).Row;
            if (myGrid2.RowCount == 0)
            {
                MessageBox.Show($"请先扫一个{_ypRow["Yp_Name"]}的追溯码才能复制", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            DataRow tracRow = (_tracView[myGrid2.Row]).Row;
            if (_ypRow["Mx_XsDw"] + "" != _ypRow["Min_Units"] + "")
            {
                if (MessageBox.Show($"药品:{_ypRow["Yp_Name"]}【编码:{_ypRow["Mx_Code"]}，销售单位:{_ypRow["Mx_XsDw"]}，制剂单位:{_ypRow["Min_Units"]}】，属于非拆零药品，是否继续复制追溯码?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No) return;
            }
            int cnt = Convert.ToInt32(_ypRow[Field_Sl]) - 1;
            if (UIInputDialog.InputIntegerDialog(ref cnt, true, "请输入数字", showMask: false))
            {
                for (int i1 = 0; i1 < cnt; i1++)
                {
                    if (ScanTraceCode(tracRow["drug_trac_codg"] + "", _ypRow) == false) return;
                }
            }
        }
        private void CmdNoTraceCode_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            string noTracCode = $"{Yb_Info.fixmedins_code.Substring(1, 2)}000000000000000000";
            ScanTraceCode(noTracCode, _ypRow);
        }

        private void CmdAllNoTrac_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            int cnt = 0;
            string noTracCode = $"{Yb_Info.fixmedins_code.Substring(1, 2)}000000000000000000";
            cnt = (int)Math.Abs(decimal.Parse(_ypRow[Field_Sl] + ""));
            for (int i1 = 0; i1 < cnt; i1++)
            {
                if (ScanTraceCode(noTracCode, _ypRow) == false) return;
            }
        }
        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            // 收集要删除的行
            List<DataRow> rowsToDelete = new List<DataRow>();

            foreach (DataRow row in tracTable.AsEnumerable().Where(p => p.Field<bool>("IsCheck")))
            {
                switch (type)
                {
                    case "门诊收费":
                    case "门诊发药":
                    case "住院发药":
                        if (decimal.Parse(_ypRow[Field_Sl] + "") > 0)
                        {
                            bllCountryYbSpXsDrugtracinfo.Delete(long.Parse(row["id"] + ""));
                        }
                        else
                        {
                            bllCountryYbSpXsThDrugtracinfo.Delete(long.Parse(row["id"] + ""));
                        }
                        break;
                    case "药房入库":
                    case "药库入库":
                    case "药库批发":
                    case "药房批发":
                    case "药房退库":
                    case "药库退库":
                    case "药库科室支领":
                    case "药房科室支领":
                    case "药库调拨药房":
                    case "药房退回药库":
                        bllCountryYbSpKcDrugtracinfo.Delete(long.Parse(row["id"] + ""));
                        break;
                }
                rowsToDelete.Add(row);
            }

            // 删除收集到的行
            foreach (DataRow row in rowsToDelete)
            {
                tracTable.Rows.Remove(row);
            }
            tracTable.AcceptChanges();
            _ypRow["traccnt"] = tracTable.AsEnumerable().Where(p => p.Field<string>("jxc_code") == GenJxcCode(_ypRow) + "").Count();
        }

        private void chkAll_CheckedChanged(object sender, EventArgs e)
        {
            foreach (DataRowView row in _tracView)
            {
                row.Row["IsCheck"] = chkAll.Checked;
            }
        }
        private void myGrid1_RowColChange(object sender, C1.Win.C1TrueDBGrid.RowColChangeEventArgs e)
        {
            if (myGrid1.RowCount == 0 || _tracCm == null) return;
            _ypRow = (_ypView[myGrid1.Row]).Row;
            DataInit(GenJxcCode(_ypRow));
            chkAll.Checked = false;
        }
        private void TraceCodeSales_FormClosed(object sender, FormClosedEventArgs e)
        {
            _tracView.RowFilter = $"";
        }
        private void myGrid1_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            string traccnt = grid.Columns["traccnt"].CellValue(e.Row).ToString();
            string Sl = grid.Columns[Field_Sl].CellValue(e.Row).ToString();
            switch (e.Column.DataColumn.DataField)
            {
                case "traccnt":
                    if (!string.IsNullOrEmpty(traccnt) && decimal.Parse(traccnt) == Math.Abs(decimal.Parse(Sl)))
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.完成16;
                    }
                    break;
            }
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.RightOfText;
        }

        private void chkLength_CheckedChanged(object sender, EventArgs e)
        {
            if (_initFlag)
            {
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("Settings", "TraceCodeCheckLength", chkLength.Checked + "");
            }
        }

        private void chkCheckRk_CheckedChanged(object sender, EventArgs e)
        {
            if (_initFlag)
            {
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("Settings", "TraceCodeCheckRk", chkCheckRk.Checked + "");
            }
        }
        #endregion


    }
}
