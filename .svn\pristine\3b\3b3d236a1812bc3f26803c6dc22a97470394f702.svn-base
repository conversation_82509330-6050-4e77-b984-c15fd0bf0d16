using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace YdBaseDict
{
    public partial class Zd_Yp_Dict2 : Common.BaseForm.BaseDict22
    {
        BLL.BllZd_Yp2 _bllZdYp2 = new BllZd_Yp2();
        Model.MdlZd_Yp2 _mdlZdYp2 = new MdlZd_Yp2();
        string _dlCode = "";
        bool _isDictInvoked = true;
        public Zd_Yp_Dict2(bool insert, DataRow row, DataTable table, string dlCode, bool isDictInvoked = true)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            _dlCode = dlCode;
            _isDictInvoked = isDictInvoked;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtGg.GotFocus += new System.EventHandler(base.InputCn);
            TxtDw.GotFocus += new System.EventHandler(base.InputCn);
            TxtPzwh.GotFocus += new System.EventHandler(base.InputCn);
            TxtScqy.GotFocus += new System.EventHandler(base.InputCn);
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Zd_Yp_Dict2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true && _isDictInvoked)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数

        private void FormInit()
        {
            TxtCode.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            // 初始化下拉菜单
            CmbJx.Init();    // 初始化剂型下拉菜单
            CmbDl.Init();    // 初始化大类下拉菜单
            CmbTsyp.Init();  // 初始化特殊药品下拉菜单
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllZdYp2.MaxCode(8);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtGg.Text = "";
            CmbJx.SelectedIndex = -1;
            TxtDw.Text = "";
            TxtPzwh.Text = "";
            TxtScqy.Text = "";
            TxtMemo.Text = "";
            ChkOtc.Checked = false;
            ChkLc.Checked = false;
            CmbDl.SelectedValue = base.MyTreeNode.Name;
            CmbTsyp.SelectedIndex = -1;

            TxtName.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["Xl_Code"] + "";
            TxtName.Text = row["Yp_Name"] + "";
            TxtJc.Text = row["Yp_Jc"] + "";
            TxtGg.Text = row["Yp_Bzgg"] + "";
            CmbJx.Text = row["Yp_Jx"] + "";
            TxtDw.Text = row["Yp_Zjdw"] + "";
            TxtPzwh.Text = row["Yp_Pzwh"] + "";
            TxtScqy.Text = row["Yp_Scqy"] + "";
            TxtMemo.Text = row["Yp_Memo"] + "";
            ChkOtc.Checked = Convert.ToBoolean(row["Yp_Otc"]);
            ChkLc.Checked = Convert.ToBoolean(row["Yp_Lc"]);
            CmbDl.SelectedValue = row["Dl_Code"] + "";
            CmbTsyp.SelectedValue = row["Ts_Code"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写药品名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }

            if (string.IsNullOrEmpty(TxtGg.Text))
            {
                MessageBox.Show("请填写规格！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtGg.Select();
                return false;
            }

            if (string.IsNullOrEmpty(TxtPzwh.Text))
            {
                MessageBox.Show("请填写批准文号！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtPzwh.Select();
                return false;
            }

            if (string.IsNullOrEmpty(TxtScqy.Text))
            {
                MessageBox.Show("请填写生产厂家！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtScqy.Select();
                return false;
            }

            return true;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();
            _mdlZdYp2.Dl_Code = CmbDl.SelectedValue.ToString();
            _mdlZdYp2.Xl_Code = _bllZdYp2.MaxCode(8);
            TxtCode.Text = _mdlZdYp2.Xl_Code;
            _mdlZdYp2.Yp_Name = TxtName.Text.Trim();
            _mdlZdYp2.Yp_Jc = TxtJc.Text.Trim();
            _mdlZdYp2.Yp_Bzgg = TxtGg.Text.Trim();
            _mdlZdYp2.Yp_Jx = CmbJx.Text.Trim();
            _mdlZdYp2.Yp_Zjdw = TxtDw.Text.Trim();
            _mdlZdYp2.Yp_Pzwh = TxtPzwh.Text.Trim();
            _mdlZdYp2.Yp_Scqy = TxtScqy.Text.Trim();
            _mdlZdYp2.Yp_Jc = TxtName.Text.Trim(); // 简称默认使用名称
            _mdlZdYp2.Yp_Memo = TxtMemo.Text.Trim();
            _mdlZdYp2.Yp_Otc = ChkOtc.Checked;
            _mdlZdYp2.Yp_Lc = ChkLc.Checked;
            _mdlZdYp2.Ts_Code = CmbTsyp.SelectedValue?.ToString() ?? "";
            _mdlZdYp2.Sc_Finish = false;

            PropertyInfo[] pis = typeof(Model.MdlZd_Yp2).GetProperties();
            foreach (PropertyInfo pi in pis)
            {
                //得到属性的值
                object value1 = pi.GetValue(_mdlZdYp2, null);
                //得到属性的名称
                string attr = pi.Name.ToString();
                if (base.MyTable.Columns.Contains(attr))
                {
                    base.MyRow[attr] = value1;
                }
            }

            //数据保存
            try
            {
                base.MyTable.Rows.Add(base.MyRow);
                _bllZdYp2.Add(_mdlZdYp2);

                base.MyTransmitTxt.OnSetText("最后");

                //父节点+1
                Common.TreeNodeHelper.UpdateNode(this.MyTreeNode, 1);
                //根节点+1
                Common.TreeNodeHelper.UpdateNode(this.MyTreeNode.TreeView.TopNode, 1);

                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            if (_isDictInvoked)
            {
                DataClear();
            }
            else
            {
                this.Close();
            }
        }

        private void DataEdit()
        {
            _mdlZdYp2 = _bllZdYp2.GetModel(base.MyRow["Xl_Code"].ToString());
            _mdlZdYp2.Dl_Code = CmbDl.SelectedValue.ToString();
            _mdlZdYp2.Xl_Code = TxtCode.Text.Trim();
            _mdlZdYp2.Yp_Name = TxtName.Text.Trim();
            _mdlZdYp2.Yp_Jc = TxtJc.Text.Trim();
            _mdlZdYp2.Yp_Bzgg = TxtGg.Text.Trim();
            _mdlZdYp2.Yp_Jx = CmbJx.Text.Trim();
            _mdlZdYp2.Yp_Zjdw = TxtDw.Text.Trim();
            _mdlZdYp2.Yp_Pzwh = TxtPzwh.Text.Trim();
            _mdlZdYp2.Yp_Scqy = TxtScqy.Text.Trim();
            _mdlZdYp2.Yp_Jc = TxtName.Text.Trim(); // 简称默认使用名称
            _mdlZdYp2.Yp_Memo = TxtMemo.Text.Trim();
            _mdlZdYp2.Yp_Otc = ChkOtc.Checked;
            _mdlZdYp2.Yp_Lc = ChkLc.Checked;
            _mdlZdYp2.Ts_Code = CmbTsyp.SelectedValue?.ToString() ?? "";

            PropertyInfo[] pis = typeof(Model.MdlZd_Yp2).GetProperties();
            foreach (PropertyInfo pi in pis)
            {
                //得到属性的值
                object value1 = pi.GetValue(_mdlZdYp2, null);
                //得到属性的名称
                string attr = pi.Name.ToString();
                if (base.MyTable.Columns.Contains(attr))
                {
                    base.MyRow[attr] = value1;
                }
            }

            //数据保存
            try
            {
                _bllZdYp2.Update(_mdlZdYp2);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 控件动作
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck())
            {
                if (base.Insert == true)
                {
                    DataAdd();
                }
                else
                {
                    DataEdit();
                }
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }
        #endregion
    }
}
