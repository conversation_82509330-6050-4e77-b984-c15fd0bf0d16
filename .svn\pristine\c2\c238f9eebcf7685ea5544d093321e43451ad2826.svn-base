﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Windows.Forms;
using BLL;
using Model;
using Top.Api.Response;

namespace YdTraceCode
{
    public class TraceCodeJxcFunc
    {
        private static BLL.BllYk_RkDrugtracinfo _bllYkRkDrugtracinfo = new BllYk_RkDrugtracinfo();
        private static int _rk_id = -1;
        /// <summary>
        /// 根据追溯码药库入库
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="traceCode"></param>
        public static bool YkRkByTraceCode(bool UseMsfx, DataTable dt, DataTable tracTable, string traceCode, string Rk_Code, string Kh_Name, ref string msg, out List<string> listTraceCode)
        {
            BllYk_Rk2 bllRk2 = new BllYk_Rk2();
            return RkByTraceCodeCommon(UseMsfx, dt, tracTable, traceCode, Rk_Code, Kh_Name, ref msg, out listTraceCode,
                "药库入库", bllRk2,
                (r, m) => ((BllYk_Rk2)r).Add((MdlYk_Rk2)m),
                (r, id) => ((BllYk_Rk2)r).GetModel(id),
                (r, m) => ((BllYk_Rk2)r).Update((MdlYk_Rk2)m),
                typeof(MdlYk_Rk2));
        }
        /// <summary>
        /// 通用追溯码入库处理方法
        /// </summary>
        private static bool RkByTraceCodeCommon(
            bool UseMsfx,
            DataTable dt,
            DataTable tracTable,
            string traceCode,
            string Rk_Code,
            string Kh_Name,
            ref string msg,
            out List<string> listTraceCode,
            string jxcType,
            object bllRk2,
            Func<object, object, int> addFunc,
            Func<object, int, object> getModelFunc,
            Action<object, object> updateFunc,
            Type mdlType)
        {
            listTraceCode = new List<string>();
            if (tracTable.AsEnumerable().Any(p => p["drug_trac_codg"].ToString() == traceCode))
            {
                msg = $"追溯码【{traceCode}】在此入库单已扫过";
                return false;
            }
            var codeFullInfoDtoList = new List<AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain>();
            bool isSuccess = false;
            if (UseMsfx)
            {
                isSuccess = TraceCodeFunc.GetTraceCodeInfo(traceCode, Kh_Name, ref msg, out codeFullInfoDtoList);
                if (!isSuccess)
                {
                    return false;
                }
            }
            else
            {
                var item = new AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain();
                item.Code = traceCode;
                codeFullInfoDtoList.Add(item);
            }

            foreach (var item in codeFullInfoDtoList)
            {
                if (tracTable.AsEnumerable().Any(p => p["drug_trac_codg"].ToString() == item.Code))
                {
                    continue;
                }
                Model.MdlZd_Yp2 mdlZdMlYp2;

                isSuccess = TraceCodeFunc.GetOrCreateYpInfo(item, out mdlZdMlYp2);
                if (!isSuccess)
                {
                    continue;
                }

                DataRow SubItemRow = null;
                bool isExist = false;
                DateTime ProduceDate = DateTime.ParseExact(item.CodeProduceInfoDTO.ProduceInfoList[0].ProduceDateStr, "yyyyMMdd", CultureInfo.InvariantCulture);
                DateTime ExpireDate = DateTime.ParseExact(item.CodeProduceInfoDTO.ProduceInfoList[0].ExpireDate, "yyyyMMdd", CultureInfo.InvariantCulture);
                string batchNo = item.CodeProduceInfoDTO.ProduceInfoList[0].BatchNo;

                if (dt.AsEnumerable().Any(p => p["Xl_Code"].ToString() == mdlZdMlYp2.Xl_Code
                && p["Yp_Scph"].ToString() == batchNo
                && DateTime.Parse(p["Yp_ScDate2"].ToString()) == ExpireDate
                && DateTime.Parse(p["Yp_ScDate1"].ToString()) == ProduceDate))
                {
                    isExist = true;
                    SubItemRow = dt.AsEnumerable().FirstOrDefault(p => p["Xl_Code"].ToString() == mdlZdMlYp2.Xl_Code
                    && p["Yp_Scph"].ToString() == batchNo
                    && DateTime.Parse(p["Yp_ScDate2"].ToString()) == ExpireDate
                    && DateTime.Parse(p["Yp_ScDate1"].ToString()) == ProduceDate);
                }
                else
                {
                    isExist = false;
                    SubItemRow = dt.NewRow();
                }
                using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                {
                    if (!isExist)
                    {
                        decimal YpCgj = 0;
                        decimal YpXsj = 0;
                        decimal YpPfj = 0;
                        AddRkDj frm = new AddRkDj(mdlZdMlYp2.Xl_Code, "");
                        frm.ShowDialog();
                        if (frm.DialogResult == DialogResult.OK)
                        {
                            YpCgj = frm.YpCgj;
                        }
                        else
                        {
                            continue;
                        }

                        SubItemRow["Rk_Code"] = Rk_Code;   //入库编码
                        SubItemRow["Xl_Code"] = mdlZdMlYp2.Xl_Code;
                        SubItemRow["Yp_Code"] = "";
                        SubItemRow["Yp_Scph"] = batchNo;
                        SubItemRow["Yp_ScDate1"] = ProduceDate;
                        SubItemRow["Yp_ScDate2"] = ExpireDate;
                        SubItemRow["Rk_Sl"] = TraceCodeToSl(jxcType, mdlZdMlYp2);
                        SubItemRow["Rk_Sl"] = TraceCodeToSl(jxcType, mdlZdMlYp2);
                        SubItemRow["Rk_Dj"] = YpCgj;
                        SubItemRow["Rk_Money"] = Common.MathFormula.Multiply(SubItemRow["Rk_Sl"], SubItemRow["Rk_Dj"]);
                        SubItemRow["Rk_Memo"] = "";

                        // 使用正确的泛型方法调用
                        var methodInfo = typeof(Common.DataTableToList).GetMethod("ToModel", new[] { typeof(DataRow) });
                        var genericMethod = methodInfo.MakeGenericMethod(mdlType);
                        var mdlRk2 = genericMethod.Invoke(null, new object[] { SubItemRow });
                        _rk_id = -1;

                        addFunc(mdlRk2, bllRk2);

                        SubItemRow["Rk_Id"] = _rk_id;
                        dt.Rows.Add(SubItemRow);
                    }
                    else
                    {
                        var mdlRk2 = getModelFunc(bllRk2, int.Parse(SubItemRow["Rk_Id"] + ""));
                        SubItemRow["Rk_Sl"] = Convert.ToDecimal(SubItemRow["Rk_Sl"]) + TraceCodeToSl(jxcType, mdlZdMlYp2);
                        SubItemRow["Rk_Money"] = Common.MathFormula.Multiply(SubItemRow["Rk_Sl"], SubItemRow["Rk_Dj"]);

                        // 使用正确的SetValue方法
                        var propRkSl = mdlRk2.GetType().GetProperty("Rk_Sl");
                        propRkSl.SetValue(mdlRk2, Convert.ToDecimal(SubItemRow["Rk_Sl"]), null);

                        var propRkMoney = mdlRk2.GetType().GetProperty("Rk_Money");
                        propRkMoney.SetValue(mdlRk2, Common.MathFormula.Multiply(SubItemRow["Rk_Sl"], SubItemRow["Rk_Dj"]), null);

                        updateFunc(bllRk2, mdlRk2);
                    }
                    dt.AcceptChanges();
                    DataRow row = tracTable.NewRow();
                    row["Rk_Id"] = _rk_id;
                    row["Rk_Code"] = Rk_Code;
                    row["drug_trac_codg"] = item.Code;
                    MdlYk_RkDrugtracinfo mdl = new MdlYk_RkDrugtracinfo();
                    mdl = Common.DataTableToList.ToModel<MdlYk_RkDrugtracinfo>(row);
                    tracTable.Rows.Add(row);
                    tracTable.AcceptChanges();
                    SubItemRow["traccnt"] = tracTable.AsEnumerable().Where(p => p.Field<string>("jxc_code") == TraceCodeFunc.GenJxcCode(jxcType, SubItemRow) + "").Count();
                    listTraceCode.Add(item.Code);
                    scope.Complete();
                }
            }
            return true;
        }

        private static decimal TraceCodeToSl(string type, MdlZd_Yp2 mdlZdMlYp2)
        {
            decimal sl = 1;
            // bool IsChaiLing = false;
            // if (type == "药库入库")
            // {
            //     if (mdlZdMlYp3.Mx_CgDw == mdlZdMlYp3.Min_Units) //药库单位等于制剂单位为拆零
            //     {
            //         IsChaiLing = true;
            //     }
            //     else
            //     {
            //         IsChaiLing = false;
            //     }
            //     sl = 1;
            // }
            // else
            // {
            //     if (mdlZdMlYp3.Mx_XsDw == mdlZdMlYp3.Min_Units) //销售单位等于制剂单位为拆零
            //     {
            //         IsChaiLing = true;
            //     }
            //     else
            //     {
            //         IsChaiLing = false;
            //     }
            //     sl = mdlZdMlYp3.Mx_Cfbl.Value;
            // }
            // if (IsChaiLing)
            // {
            //     sl = sl * (mdlZdMlYp3.Amount_Per_Package != null ? mdlZdMlYp3.Amount_Per_Package.Value : 1);
            // }
            return sl;
        }
    }
}
