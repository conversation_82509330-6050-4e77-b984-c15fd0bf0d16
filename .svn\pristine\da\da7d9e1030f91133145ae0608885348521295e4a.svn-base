﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlZd_Yp2.cs
*
* 功 能： N/A
* 类 名： MdlZd_Yp2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:26   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlZd_Yp2:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlZd_Yp2
	{
		public MdlZd_Yp2()
		{ }
		#region Model
		private string _dl_code;
		private string _xl_code;
		private string _tm_code;
		private string _yp_pzwh;
		private string _yp_name;
		private string _yp_jc;
		private string _yp_zjgg;
		private string _yp_zjdw;
		private string _yp_jx;
		private string _yp_bzgg;
		private int? _yp_bzzhb = 0;
		private string _yp_scqy;
		private decimal? _yp_xsdj = 0M;
		private string _ts_code;
		private bool _yp_otc;
		private bool _yp_lc = false;
		private string _yp_memo;
		private int? _xl_count = 0;
		private bool _sc_finish;
		private string _drug_identification_code;
		/// <summary>
		/// 
		/// </summary>
		public string Dl_Code
		{
			set { _dl_code = value; }
			get { return _dl_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Xl_Code
		{
			set { _xl_code = value; }
			get { return _xl_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Tm_Code
		{
			set { _tm_code = value; }
			get { return _tm_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Pzwh
		{
			set { _yp_pzwh = value; }
			get { return _yp_pzwh; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Name
		{
			set { _yp_name = value; }
			get { return _yp_name; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Jc
		{
			set { _yp_jc = value; }
			get { return _yp_jc; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Zjgg
		{
			set { _yp_zjgg = value; }
			get { return _yp_zjgg; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Zjdw
		{
			set { _yp_zjdw = value; }
			get { return _yp_zjdw; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Jx
		{
			set { _yp_jx = value; }
			get { return _yp_jx; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Bzgg
		{
			set { _yp_bzgg = value; }
			get { return _yp_bzgg; }
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Yp_Bzzhb
		{
			set { _yp_bzzhb = value; }
			get { return _yp_bzzhb; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Scqy
		{
			set { _yp_scqy = value; }
			get { return _yp_scqy; }
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Yp_Xsdj
		{
			set { _yp_xsdj = value; }
			get { return _yp_xsdj; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ts_Code
		{
			set { _ts_code = value; }
			get { return _ts_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Yp_Otc
		{
			set { _yp_otc = value; }
			get { return _yp_otc; }
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Yp_Lc
		{
			set { _yp_lc = value; }
			get { return _yp_lc; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Memo
		{
			set { _yp_memo = value; }
			get { return _yp_memo; }
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Xl_Count
		{
			set { _xl_count = value; }
			get { return _xl_count; }
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Sc_Finish
		{
			set { _sc_finish = value; }
			get { return _sc_finish; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Drug_Identification_Code
		{
			set { _drug_identification_code = value; }
			get { return _drug_identification_code; }
		}
		#endregion Model

	}
}

