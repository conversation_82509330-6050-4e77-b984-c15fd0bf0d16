﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8DEE3CA3-DDE2-40B7-8E77-BC5D0FCE94C1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>IDAL</RootNamespace>
    <AssemblyName>IDAL</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="IDalBb1.cs" />
    <Compile Include="IDalBb2.cs" />
    <Compile Include="IDalDd1.cs" />
    <Compile Include="IDalDd2.cs" />
    <Compile Include="IDalKc_Pd1.cs" />
    <Compile Include="IDalSysMenu1.cs" />
    <Compile Include="IDalSysMenu2.cs" />
    <Compile Include="IDalSysModule.cs" />
    <Compile Include="IDalSysModuleAuth.cs" />
    <Compile Include="IDalSysPara.cs" />
    <Compile Include="IDalSysRole.cs" />
    <Compile Include="IDalSysRoleAuth.cs" />
    <Compile Include="IDalSysRoleModule.cs" />
    <Compile Include="IDalSysRpt.cs" />
    <Compile Include="IDalSysRpt_Class.cs" />
    <Compile Include="IDalSysRpt_Class_Level.cs" />
    <Compile Include="IDalYk_Ck1.cs" />
    <Compile Include="IDalYk_Rk1.cs" />
    <Compile Include="IDalYk_Rk2.cs" />
    <Compile Include="IDalYk_RkDrugtracinfo.cs" />
    <Compile Include="IDalYp_Bhg.cs" />
    <Compile Include="IDalZd_Blfy.cs" />
    <Compile Include="IDalZd_Cctj.cs" />
    <Compile Include="IDalZd_Cs.cs" />
    <Compile Include="IDalZd_Czy.cs" />
    <Compile Include="IDalZd_Hj.cs" />
    <Compile Include="IDalZd_Hy.cs" />
    <Compile Include="IDalZd_Jb.cs" />
    <Compile Include="IDalZd_Jx.cs" />
    <Compile Include="IDalZd_Kh.cs" />
    <Compile Include="IDalZd_Kh_Ywy.cs" />
    <Compile Include="IDalZd_KjDw.cs" />
    <Compile Include="IDalZd_Pay.cs" />
    <Compile Include="IDalZd_PxJh.cs" />
    <Compile Include="IDalZd_PxJl.cs" />
    <Compile Include="IDalZd_SbJc.cs" />
    <Compile Include="IDalZd_Sfys.cs" />
    <Compile Include="IDalZd_TjJh.cs" />
    <Compile Include="IDalZd_TjJl.cs" />
    <Compile Include="IDalZd_Wsd.cs" />
    <Compile Include="IDalZd_Xq.cs" />
    <Compile Include="IDalZd_Yd.cs" />
    <Compile Include="IDalZd_YdJsr.cs" />
    <Compile Include="IDalZd_Yd_Mdzy.cs" />
    <Compile Include="IDalZd_Yd_Qtry.cs" />
    <Compile Include="IDalZd_Yd_Xkzbg.cs" />
    <Compile Include="IDalZd_Yd_ZlFzr.cs" />
    <Compile Include="IDalZd_Yh.cs" />
    <Compile Include="IDalZd_Ylqx.cs" />
    <Compile Include="IDalZd_Yp1.cs" />
    <Compile Include="IDalZd_Yp2.cs" />
    <Compile Include="IDalZd_Yp2_Ph.cs" />
    <Compile Include="IDalZd_Yp2_Ts.cs" />
    <Compile Include="IDalZd_Yp3.cs" />
    <Compile Include="IDalZd_Yp4.cs" />
    <Compile Include="IDalZd_ZdYh.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBModel\YBModel.csproj">
      <Project>{3cb4dc25-92a0-49f4-a946-d9988b409c7c}</Project>
      <Name>YBModel</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>