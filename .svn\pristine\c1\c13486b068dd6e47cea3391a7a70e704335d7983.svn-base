﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlYk_Rk2.cs
*
* 功 能： N/A
* 类 名： MdlYk_Rk2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/8/1 9:27:19   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace  Model
{
	/// <summary>
	/// MdlYk_Rk2:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlYk_Rk2
	{
		public MdlYk_Rk2()
		{}
		#region Model
		private int _rk_id;
		private string _rk_code;
		private string _xl_code;
		private string _yp_code;
		private string _yp_scph;
		private DateTime? _yp_scdate1;
		private DateTime? _yp_scdate2;
		private decimal? _rk_sl=0M;
		private decimal? _rk_dj=0M;
		private decimal? _rk_money=0M;
		private string _rk_memo;
		/// <summary>
		/// 
		/// </summary>
		public int Rk_Id
		{
			set{ _rk_id=value;}
			get{return _rk_id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Rk_Code
		{
			set{ _rk_code=value;}
			get{return _rk_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Xl_Code
		{
			set{ _xl_code=value;}
			get{return _xl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Code
		{
			set{ _yp_code=value;}
			get{return _yp_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Scph
		{
			set{ _yp_scph=value;}
			get{return _yp_scph;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Yp_ScDate1
		{
			set{ _yp_scdate1=value;}
			get{return _yp_scdate1;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Yp_ScDate2
		{
			set{ _yp_scdate2=value;}
			get{return _yp_scdate2;}
		}
		/// <summary>
		/// 药房入库_数量
		/// </summary>
		public decimal? Rk_Sl
		{
			set{ _rk_sl=value;}
			get{return _rk_sl;}
		}
		/// <summary>
		/// 药房入库_单价
		/// </summary>
		public decimal? Rk_Dj
		{
			set{ _rk_dj=value;}
			get{return _rk_dj;}
		}
		/// <summary>
		/// 药房入库_金额
		/// </summary>
		public decimal? Rk_Money
		{
			set{ _rk_money=value;}
			get{return _rk_money;}
		}
		/// <summary>
		/// 药房入库_备注
		/// </summary>
		public string Rk_Memo
		{
			set{ _rk_memo=value;}
			get{return _rk_memo;}
		}
		#endregion Model

	}
}

