﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Top.Api;
using Top.Api.Request;
using Top.Api.Response;
using static Top.Api.Response.AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse;
using Model;
using BLL;
using System.Text.RegularExpressions;
using Common;
using System.Globalization;
using System.Transactions;
using Sunny.UI;
using YdBaseDict;
namespace YdTraceCode
{
    public class TraceCodeFunc
    {
        public static bool CheckMsfxConfig()
        {
            if (string.IsNullOrEmpty(YdPara.ApiConfig.MSFX.AppKey) || string.IsNullOrEmpty(YdPara.ApiConfig.MSFX.AppSecret) || string.IsNullOrEmpty(YdPara.ApiConfig.MSFX.EntId))
            {
                MessageBox.Show("请配置先配置码上放心参数", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            return true;
        }

        #region 获取追溯码信息
        /// <summary>
        /// 获取追溯码信息
        /// </summary>
        /// <param name="traceCode"></param>
        /// <param name="msg"></param>
        /// <param name="codeFullInfoDtoList"></param>
        /// <returns></returns>
        public static bool GetTraceCodeInfo(string traceCode, string Kh_Name, ref string msg, out List<AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain> codeFullInfoDtoList)
        {
            ITopClient client = CreateTopClient();
            AlibabaAlihealthDrugtraceTopLsydQueryCodedetailRequest req = new AlibabaAlihealthDrugtraceTopLsydQueryCodedetailRequest();
            req.RefEntId = YdPara.ApiConfig.MSFX.EntId;
            req.Codes = traceCode;
            AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse rsp = client.Execute(req);
            codeFullInfoDtoList = null;
            if (rsp.IsError)
            {
                msg = $"追溯码查询失败\n{rsp.ErrMsg}";
                return false;
            }

            if (rsp.Result.Models.Count == 0)
            {
                msg = "追溯码查询结果为空";
                return false;
            }

            var model = rsp.Result.Models[0];
            //小码
            if (model.PackageLevel == "1")
            {
                codeFullInfoDtoList = new List<AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain> { model };
            }
            //中码
            else if (model.PackageLevel == "2")
            {
                return GetBigTraceCodeInfo(traceCode, Kh_Name, ref msg, out codeFullInfoDtoList);
            }
            //大码
            else if (model.PackageLevel == "3")
            {
                return GetBigTraceCodeInfo(traceCode, Kh_Name, ref msg, out codeFullInfoDtoList);
            }
            else
            {
                msg = "追溯码查询结果异常";
                return false;
            }
            return true;
        }
        private static bool GetBigTraceCodeInfo(string traceCode, string Kh_Name, ref string msg, out List<AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain> codeFullInfoDtoList)
        {
            ITopClient client = CreateTopClient();
        t:
            AlibabaAlihealthDrugtraceTopLsydQueryRelationRequest req = new AlibabaAlihealthDrugtraceTopLsydQueryRelationRequest();
            req.RefEntId = YdPara.ApiConfig.MSFX.EntId;
            req.Code = traceCode;
            req.DesRefEntId = YdPara.ApiConfig.MSFX.EntId;
            AlibabaAlihealthDrugtraceTopLsydQueryRelationResponse rsp = client.Execute(req);
            codeFullInfoDtoList = new List<AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain>();
            if (rsp.IsError)
            {
                msg = $"追溯码大码查询失败\n{rsp.ErrMsg}";
                return false;
            }
            if (!rsp.Result.ResponseSuccess)
            {
                if (rsp.Result.MsgInfo.Contains("非本企业经营的追溯码"))
                {
                    if (!TryUploadInOutBill(traceCode, Kh_Name, ref msg))
                    {
                        return false;
                    }
                    goto t;
                }
                else
                {
                    msg = $"追溯码大码查询失败\n{rsp.Result.MsgInfo}";
                    return false;
                }
            }
            else
            {
                foreach (var model in rsp.Result.ModelList[0].CodeRelationList)
                {
                    if (model.CodePackLevel == "1")
                    {
                        List<AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain> list = new List<AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain>();
                        if (!GetTraceCodeInfo(model.Code, Kh_Name, ref msg, out list))
                        {
                            continue;
                        }
                        codeFullInfoDtoList.AddRange(list);
                    }
                    if (model.CodePackLevel == "2" && model.Code != traceCode)
                    {
                        GetBigTraceCodeInfo(model.Code, Kh_Name, ref msg, out codeFullInfoDtoList);
                    }
                }
                return true;
            }
        }
        private static bool GetEntId(string Kh_Name, ref string msg, out string EntId)
        {
            ITopClient client = CreateTopClient();
            AlibabaAlihealthDrugtraceTopLsydQueryGetentinfoRequest req = new AlibabaAlihealthDrugtraceTopLsydQueryGetentinfoRequest();
            req.EntName = Kh_Name;
            EntId = "";
            AlibabaAlihealthDrugtraceTopLsydQueryGetentinfoResponse rsp = client.Execute(req);
            if (rsp.IsError)
            {
                msg = $"供应商【{Kh_Name}】查询失败\n{rsp.ErrMsg}";
                return false;
            }
            if (rsp.Result.ResponseSuccess)
            {
                EntId = rsp.Result.Model.EntId;
                return true;
            }
            else
            {
                msg = $"供应商【{Kh_Name}】查询失败\n{rsp.Result.MsgInfo}";
                return false;
            }
        }
        /// <summary>
        /// 上传出入库单据
        /// </summary>
        private static bool TryUploadInOutBill(string traceCode, string Kh_Name, ref string msg)
        {
            // 获取企业ID
            if (!GetEntId(Kh_Name, ref msg, out string entId))
            {
                return false;
            }

            // 创建并上传入库单据
            ITopClient client = CreateTopClient();
            AlibabaAlihealthDrugtraceTopLsydUploadinoutbillRequest req = new AlibabaAlihealthDrugtraceTopLsydUploadinoutbillRequest
            {
                BillCode = DateTime.Now.ToString("yyyyMMddHHmmssfff"),
                BillTime = DateTime.Now,
                BillType = 102L,
                PhysicType = 3L,
                RefUserId = YdPara.ApiConfig.MSFX.EntId,
                // AgentRefUserId = "";
                FromUserId = entId,
                ToUserId = YdPara.ApiConfig.MSFX.EntId,
                // DestUserId = "";
                // OperIcCode = "210000234";
                // OperIcName = "张三";
                // WarehouseId = "W001";
                // DrugId = "D001";
                TraceCodes = traceCode,
                ClientType = "2",
                // ReturnReasonCode = "1";
                // ReturnReasonDes = "退货原因描述";
                // CancelReasonCode = "1";
                // CancelReasonDes = "注销原因描述";
                // ExecuterName = "执行人";
                // ExecuterCode = "11034564321";
                // SuperviserName = "监督人";
                // SuperviserCode = "11276789342";
                // FromAddress = "发货地址XXX";
                // ToAddress = "收货地址XXX";
                // FromBillCode = "123456";
                // OrderCode = "123456";
                // FromPerson = "张某";
                // ToPerson = "李某";
                // DisRefEntId = "5069452c34b94a778abaa26c2b40b305";
                // DisEntId = "5069452c34b94a778abaa26c2b40b305";
                // QuReceivable = 10L;
                // XtIsCheck = "0";
                // XtCheckCode = "未验证通过原因";
                // XtCheckCodeDesc = "未通过原因描述";
                // DrugListJson = "[{\"codeCount\":100,\"commDrugId\":\"testCommDrugId0\",\"exprieDate\":1571131734945,\"physicInfo\":\"test0\",\"pkgSpec\":\"test0\",\"prepnCount\":10,\"produceBatchNo\":\"test0\",\"produceDate\":1571131734945},{\"codeCount\":100,\"commDrugId\":\"testCommDrugId1\",\"exprieDate\":1571131734945,\"physicInfo\":\"test1\",\"pkgSpec\":\"test1\",\"prepnCount\":10,\"produceBatchNo\":\"test1\",\"produceDate\":1571131734945}]";
                // AssRefEntId = "5069452c34b94a778abaa26c2b40b305";
                // AssEntId = "5069452c34b94a778abaa26c2b40b305";
            };

            AlibabaAlihealthDrugtraceTopLsydUploadinoutbillResponse rsp = client.Execute(req);

            if (rsp.IsError)
            {
                msg = $"追溯码上传失败\n{rsp.ErrMsg}";
                return false;
            }

            if (!rsp.ResponseSuccess)
            {
                msg = $"追溯码上传失败\n{rsp.MsgInfo}";
                return false;
            }

            return true;
        }
        /// <summary>
        /// 创建阿里健康追溯平台客户端
        /// </summary>
        private static ITopClient CreateTopClient()
        {
            return new DefaultTopClient(
                YdPara.ApiConfig.MSFX.ApiUrl,
                YdPara.ApiConfig.MSFX.AppKey,
                YdPara.ApiConfig.MSFX.AppSecret
            );
        }
        #endregion

        #region 获取或创建药品信息
        /// <summary>
        /// 获取或创建药品信息
        /// </summary>
        /// <param name="codeFullInfoDto">追溯码信息</param>
        /// <param name="mdlZdMlYp2">药品基本信息模型</param>
        /// <param name="mdlZdMlYp3">药品详细信息模型</param>
        /// <returns>是否成功获取或创建药品信息</returns>
        public static bool GetOrCreateYpInfo(AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain codeFullInfoDto, out Model.MdlZd_Yp2 mdlZdMlYp2)
        {
            BLL.BllZd_Yp2 bllZdMlYp2 = new BLL.BllZd_Yp2();
            BLL.BllZd_Jx bllZdJx = new BLL.BllZd_Jx();
            Chs2Spell chs2Spell = new Chs2Spell();

            // 先尝试通过GetYpInfo获取现有药品信息
            bool isSuccess = GetYpInfo(codeFullInfoDto, out mdlZdMlYp2);
            if (isSuccess)
            {
                return true;
            }

            // 创建药品详细信息
            mdlZdMlYp2 = new Model.MdlZd_Yp2();
            // Yp_Pzwh varchar(100)	Checked
            // Yp_Name varchar(100)    Checked
            // Yp_Jc   varchar(100)    Checked
            // Yp_Zjgg varchar(200)    Checked
            // Yp_Zjdw varchar(50) Checked
            // Yp_Jx   varchar(50) Checked
            // Yp_Bzgg varchar(50) Checked
            // Yp_Bzzhb    int Checked
            // Yp_Scqy varchar(200)	Checked
            // Yp_Xsdj numeric(10, 4)  Checked
            // Ts_Code char(2) Checked
            // Yp_Otc  bit Checked
            // Yp_Lc bit Checked
            mdlZdMlYp2.Yp_Pzwh = codeFullInfoDto.DrugEntBaseDTO.ApprovalLicenceNo;
            mdlZdMlYp2.Yp_Name = codeFullInfoDto.DrugEntBaseDTO.PhysicName;
            mdlZdMlYp2.Yp_Jc = chs2Spell.GetPy(mdlZdMlYp2.Yp_Name);
            mdlZdMlYp2.Yp_Zjgg = codeFullInfoDto.DrugEntBaseDTO.PrepnSpec;
            mdlZdMlYp2.Yp_Zjdw = codeFullInfoDto.DrugEntBaseDTO.PrepnSpec;
            mdlZdMlYp2.Yp_Jx = codeFullInfoDto.DrugEntBaseDTO.PrepnTypeDesc;
            mdlZdMlYp2.Yp_Bzgg = codeFullInfoDto.DrugEntBaseDTO.PkgSpecCrit;
            mdlZdMlYp2.Yp_Scqy = codeFullInfoDto.PUserEntDTO.EntName;

            decimal? Amount_Per_Package = null;
            string MxXsDw = "";
            string Min_Units = "";
            ParseUnitString(codeFullInfoDto.DrugEntBaseDTO.PkgSpecCrit, ref Amount_Per_Package, ref Min_Units, ref MxXsDw);

            mdlZdMlYp2.Yp_Bzzhb = Convert.ToInt32(Amount_Per_Package);
            mdlZdMlYp2.Drug_Identification_Code = codeFullInfoDto.Code.Left(7);

            // 显示添加药品对话框
            DataTable dt = bllZdMlYp2.GetList("1=2").Tables[0];
            DataRow row = dt.NewRow();
            Common.DataTableToList.ToDataRow(mdlZdMlYp2, row);
            Zd_Yp_Dict2 addYp = new Zd_Yp_Dict2(true, row, dt, "", false);
            addYp.ShowDialog();
            if (addYp.DialogResult == DialogResult.OK)
            {
                Common.DataTableToList.ToModel(row, mdlZdMlYp2);
                return true;
            }

            return false;
        }
        /// <summary>
        /// 获取药品信息
        /// </summary>
        /// <param name="codeFullInfoDto"></param>
        /// <param name="mdlZdMlYp2"></param>
        /// <param name="mdlZdMlYp3"></param>
        /// <returns></returns>
        private static bool GetYpInfo(AlibabaAlihealthDrugtraceTopLsydQueryCodedetailResponse.CodeFullInfoDtoDomain codeFullInfoDto, out Model.MdlZd_Yp2 mdlZdMlYp2)
        {
            BLL.BllZd_Yp2 bllZdMlYp2 = new BLL.BllZd_Yp2();
            mdlZdMlYp2 = null;
            List<Model.MdlZd_Yp2> list = new List<Model.MdlZd_Yp2>();
            bool isHaveBsm = true;
            //先通过药品标识码查询
            list = bllZdMlYp2.GetModelList($"CHARINDEX(Drug_Identification_Code,'{codeFullInfoDto.Code}')>0");
            if (list.Count == 0)
            {
                isHaveBsm = false;
                //标识码查不到，再通过药品批准文号查询
                list = bllZdMlYp2.GetModelList($"(CHARINDEX('{codeFullInfoDto.DrugEntBaseDTO.ApprovalLicenceNo}',Mx_Gyzz)>0 OR CHARINDEX('{codeFullInfoDto.DrugEntBaseDTO.ApprovalLicenceNo}',Mx_Gyzz)>0 )");
            }
            if (list.Count > 0)
            {
                if (list.Count == 1)
                {
                    mdlZdMlYp2 = list[0];
                    if (!isHaveBsm)
                    {
                        mdlZdMlYp2.Drug_Identification_Code = codeFullInfoDto.Code.Left(7);
                        bllZdMlYp2.Update(mdlZdMlYp2);
                    }
                    return true;
                }
                else
                {
                    // 处理多个匹配的情况
                    SelectYp selectYp = new SelectYp(list, codeFullInfoDto.DrugEntBaseDTO.ApprovalLicenceNo);
                    selectYp.ShowDialog();
                    if (selectYp.DialogResult == DialogResult.OK)
                    {
                        mdlZdMlYp2 = selectYp.MdlZdMlYp2;
                        mdlZdMlYp2.Drug_Identification_Code = codeFullInfoDto.Code.Left(7);
                        bllZdMlYp2.Update(mdlZdMlYp2);
                        return true;
                    }
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// 解析规格字符串
        /// </summary>
        /// <param name="input"></param>
        /// <param name="num"></param>
        /// <param name="unit1"></param>
        /// <param name="unit2"></param>
        /// <returns></returns>
        private static bool ParseUnitString(string input, ref decimal? num, ref string unit1, ref string unit2)
        {
            var match = Regex.Match(input, @"^(\d+)(\D+)/(\D+)$");
            if (match.Success)
            {
                num = decimal.Parse(match.Groups[1].Value);
                unit1 = match.Groups[2].Value;
                unit2 = match.Groups[3].Value;
                return true;
            }
            return false;
        }
        #endregion

        public static string GenJxcCode(string type, DataRow _ypRow)
        {
            string Field_Sl = "";
            string Field_Lb = "";
            string Field_Id = "";
            string Field_Code = "";
            string Field_Ex = "";
            switch (type)
            {
                case "门诊收费":
                case "门诊发药":
                    Field_Sl = "Mz_Sl";
                    Field_Lb = "Mz_Lb";
                    Field_Id = "Mz_Id";
                    Field_Code = "Mz_Code";
                    Field_Ex = "MZ";
                    break;
                case "药房入库":
                    Field_Sl = "Rk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Rk_Code";
                    Field_Ex = "YFRK";
                    break;
                case "药库入库":
                    Field_Sl = "Rk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Rk_Code";
                    Field_Ex = "YKRK";
                    break;
                case "药库批发":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YKPF";
                    break;
                case "药房批发":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YFPF";
                    break;
                case "药库退库":
                    Field_Sl = "Tk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Tk_Code";
                    Field_Ex = "YKTK";
                    break;
                case "药房退库":
                    Field_Sl = "Tk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Tk_Code";
                    Field_Ex = "YFTK";
                    break;
                case "药库科室支领":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YKKS";
                    break;
                case "药房科室支领":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YFKS";
                    break;
                case "药库调拨药房":
                    Field_Sl = "Ck_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Ck_Code";
                    Field_Ex = "YKYF";
                    break;
                case "药房退回药库":
                    Field_Sl = "Tk_Sl";
                    Field_Lb = "Dl_Name";
                    Field_Id = "Xx_Code";
                    Field_Code = "Tk_Code";
                    Field_Ex = "YFYK";
                    break;
                case "住院发药":
                    Field_Sl = "Cf_Sl";
                    Field_Lb = "Cf_Lb";
                    Field_Id = "Cf_Id";
                    Field_Code = "Cf_Code";
                    Field_Ex = "ZY";
                    break;
            }
            switch (type)
            {
                case "药库入库":
                case "药房入库":
                    return Field_Ex + _ypRow[Field_Code] + "-" + _ypRow[Field_Id];
                case "门诊收费":
                case "门诊发药":
                case "住院发药":
                case "药库批发":
                case "药房批发":
                case "药房退库":
                case "药库退库":
                case "药库科室支领":
                case "药房科室支领":
                case "药库调拨药房":
                case "药房退回药库":
                    return "";
            }
            return "";
        }

        #region 追溯码校验
        /// <summary>
        /// 追溯码校验
        /// </summary>
        /// <param name="traceCode"></param>
        /// <param name="type"></param>
        /// <param name="repeatCount"></param>
        /// <returns></returns>
        public static bool CheckTraceCode(string traceCode, string type, int repeatCount, string Xx_Code, out string msg)
        {
            msg = "";
            // if (type == "门诊收费" || type == "门诊发药" || type == "住院发药")
            // {
            //     //销售验证历史销售次数-退货次数
            //     YBBLL.BllCountry_YB_SpXs_Drugtracinfo bllCountryYbSpXsDrugtracinfo = new YBBLL.BllCountry_YB_SpXs_Drugtracinfo();
            //     YBBLL.BllCountry_YB_SpXsTh_Drugtracinfo bllCountryYbSpXsThDrugtracinfo = new YBBLL.BllCountry_YB_SpXsTh_Drugtracinfo();
            //     int saleCount = bllCountryYbSpXsDrugtracinfo.GetRecordCount($"Drug_Trac_Codg='{traceCode}'");
            //     int returnCount = bllCountryYbSpXsThDrugtracinfo.GetRecordCount($"Drug_Trac_Codg='{traceCode}'");
            //     var mdlZdMlYp3 = new BLL.BllZd_Ml_Yp3().GetModel(Xx_Code.Substring(0, 11));
            //     if (mdlZdMlYp3 != null)
            //     {
            //         //非拆零药品一个追溯码只能扫一次
            //         if (mdlZdMlYp3.Mx_XsDw != mdlZdMlYp3.Min_Units)
            //         {
            //             if (saleCount - returnCount > 1)
            //             {
            //                 msg = "非拆零药品一个追溯码只能扫一次";
            //                 return false;
            //             }
            //         }
            //         else //拆零药品扫码次数最大等于包装数量
            //         {
            //             if (mdlZdMlYp3.Amount_Per_Package != null && repeatCount + saleCount - returnCount > mdlZdMlYp3.Amount_Per_Package)
            //             {
            //                 msg = $"该拆零药品追溯码【{traceCode}】已扫【{saleCount - returnCount}】次，最多只能扫【{mdlZdMlYp3.Amount_Per_Package.Value.ToString("0.#####")}】次";
            //                 return false;
            //             }
            //         }
            //     }
            //     return true;
            // }
            return true;
        }
        #endregion
    }
}
