﻿using System;
using System.Reflection;
using System.Configuration;
using Common;
using IDAL;
namespace DALFactory
{
    /// <summary>
    /// 抽象工厂模式创建DAL。
    /// web.config 需要加入配置：(利用工厂模式+反射机制+缓存机制,实现动态创建不同的数据层对象接口) 
    /// DataCache类在导出代码的文件夹里
    /// <appSettings> 
    /// <add key="DAL" value="SQLServerDAL" /> (这里的命名空间根据实际情况更改为自己项目的命名空间)
    /// </appSettings> 
    /// </summary>
    public sealed class DataAccess//<t>
    {
        private static readonly string AssemblyPath = Common.WinFormVar.Var.DALPath;
        /// <summary>
        /// 创建对象或从缓存获取
        /// </summary>
        public static object CreateObject(string AssemblyPath, string ClassNamespace)
        {
            object objType = DataCache.GetCache(ClassNamespace);//从缓存读取
            if (objType == null)
            {
                try
                {
                    objType = Assembly.Load(AssemblyPath).CreateInstance(ClassNamespace);//反射创建
                    DataCache.SetCache(ClassNamespace, objType);// 写入缓存
                }
                catch
                { }
            }
            return objType;
        }

        /// <summary>
        /// 创建DalSysMenu1数据层接口。一级菜单
        /// </summary>
        public static IDAL.IDalSysMenu1 CreateDalSysMenu1()
        {

            string ClassNamespace = AssemblyPath + ".DalSysMenu1";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysMenu1)objType;
        }


        /// <summary>
        /// 创建DalSysMenu2数据层接口。二级菜单
        /// </summary>
        public static IDAL.IDalSysMenu2 CreateDalSysMenu2()
        {

            string ClassNamespace = AssemblyPath + ".DalSysMenu2";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysMenu2)objType;
        }


        /// <summary>
        /// 创建DalSysModule数据层接口。系统模块
        /// </summary>
        public static IDAL.IDalSysModule CreateDalSysModule()
        {

            string ClassNamespace = AssemblyPath + ".DalSysModule";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysModule)objType;
        }


        /// <summary>
        /// 创建DalSysModuleAuth数据层接口。系统模块权限
        /// </summary>
        public static IDAL.IDalSysModuleAuth CreateDalSysModuleAuth()
        {

            string ClassNamespace = AssemblyPath + ".DalSysModuleAuth";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysModuleAuth)objType;
        }


        /// <summary>
        /// 创建DalSysPara数据层接口。系统参数
        /// </summary>
        public static IDAL.IDalSysPara CreateDalSysPara()
        {

            string ClassNamespace = AssemblyPath + ".DalSysPara";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysPara)objType;
        }


        /// <summary>
        /// 创建DalSysRole数据层接口。系统角色
        /// </summary>
        public static IDAL.IDalSysRole CreateDalSysRole()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRole";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRole)objType;
        }


        /// <summary>
        /// 创建DalSysRoleAuth数据层接口。角色权限
        /// </summary>
        public static IDAL.IDalSysRoleAuth CreateDalSysRoleAuth()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRoleAuth";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRoleAuth)objType;
        }


        /// <summary>
        /// 创建DalSysRoleModule数据层接口。角色模块
        /// </summary>
        public static IDAL.IDalSysRoleModule CreateDalSysRoleModule()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRoleModule";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRoleModule)objType;
        }


        /// <summary>
        /// 创建DalSysRpt数据层接口。系统报表
        /// </summary>
        public static IDAL.IDalSysRpt CreateDalSysRpt()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRpt";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRpt)objType;
        }


        /// <summary>
        /// 创建DalSysRpt_Class数据层接口。报表类别
        /// </summary>
        public static IDAL.IDalSysRpt_Class CreateDalSysRpt_Class()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRpt_Class";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRpt_Class)objType;
        }


        /// <summary>
        /// 创建DalSysRpt_Class_Level数据层接口。报表类别规则
        /// </summary>
        public static IDAL.IDalSysRpt_Class_Level CreateDalSysRpt_Class_Level()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRpt_Class_Level";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRpt_Class_Level)objType;
        }
        
		/// <summary>
		/// 创建数据层接口
		/// </summary>
		//public static t Create(string ClassName)
		//{
		//string ClassNamespace = AssemblyPath +"."+ ClassName;
		//object objType = CreateObject(AssemblyPath, ClassNamespace);
		//return (t)objType;
		//}
		/// <summary>
		/// 创建DalBb1数据层接口。
		/// </summary>
		public static IDAL.IDalBb1 CreateDalBb1()
		{

			string ClassNamespace = AssemblyPath + ".DalBb1";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalBb1)objType;
		}


		/// <summary>
		/// 创建DalBb2数据层接口。
		/// </summary>
		public static IDAL.IDalBb2 CreateDalBb2()
		{

			string ClassNamespace = AssemblyPath + ".DalBb2";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalBb2)objType;
		}


		/// <summary>
		/// 创建DalDd1数据层接口。
		/// </summary>
		public static IDAL.IDalDd1 CreateDalDd1()
		{

			string ClassNamespace = AssemblyPath + ".DalDd1";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalDd1)objType;
		}


		/// <summary>
		/// 创建DalDd2数据层接口。
		/// </summary>
		public static IDAL.IDalDd2 CreateDalDd2()
		{

			string ClassNamespace = AssemblyPath + ".DalDd2";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalDd2)objType;
		}


		/// <summary>
		/// 创建DalKc_Pd1数据层接口。
		/// </summary>
		public static IDAL.IDalKc_Pd1 CreateDalKc_Pd1()
		{

			string ClassNamespace = AssemblyPath + ".DalKc_Pd1";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalKc_Pd1)objType;
		}


		/// <summary>
		/// 创建DalYk_Ck1数据层接口。
		/// </summary>
		public static IDAL.IDalYk_Ck1 CreateDalYk_Ck1()
		{

			string ClassNamespace = AssemblyPath + ".DalYk_Ck1";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalYk_Ck1)objType;
		}


		/// <summary>
		/// 创建DalYk_Rk1数据层接口。
		/// </summary>
		public static IDAL.IDalYk_Rk1 CreateDalYk_Rk1()
		{

			string ClassNamespace = AssemblyPath + ".DalYk_Rk1";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalYk_Rk1)objType;
		}


		/// <summary>
		/// 创建DalYp_Bhg数据层接口。
		/// </summary>
		public static IDAL.IDalYp_Bhg CreateDalYp_Bhg()
		{

			string ClassNamespace = AssemblyPath + ".DalYp_Bhg";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalYp_Bhg)objType;
		}


		/// <summary>
		/// 创建DalZd_Blfy数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Blfy CreateDalZd_Blfy()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Blfy";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Blfy)objType;
		}


		/// <summary>
		/// 创建DalZd_Cctj数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Cctj CreateDalZd_Cctj()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Cctj";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Cctj)objType;
		}


		/// <summary>
		/// 创建DalZd_Cs数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Cs CreateDalZd_Cs()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Cs";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Cs)objType;
		}


		/// <summary>
		/// 创建DalZd_Czy数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Czy CreateDalZd_Czy()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Czy";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Czy)objType;
		}


		/// <summary>
		/// 创建DalZd_Hj数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Hj CreateDalZd_Hj()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Hj";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Hj)objType;
		}


		/// <summary>
		/// 创建DalZd_Hy数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Hy CreateDalZd_Hy()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Hy";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Hy)objType;
		}


		/// <summary>
		/// 创建DalZd_Jb数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Jb CreateDalZd_Jb()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Jb";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Jb)objType;
		}


		/// <summary>
		/// 创建DalZd_Jx数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Jx CreateDalZd_Jx()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Jx";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Jx)objType;
		}


		/// <summary>
		/// 创建DalZd_Kh数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Kh CreateDalZd_Kh()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Kh";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Kh)objType;
		}


		/// <summary>
		/// 创建DalZd_Kh_Ywy数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Kh_Ywy CreateDalZd_Kh_Ywy()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Kh_Ywy";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Kh_Ywy)objType;
		}


		/// <summary>
		/// 创建DalZd_Pay数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Pay CreateDalZd_Pay()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Pay";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Pay)objType;
		}


		/// <summary>
		/// 创建DalZd_PxJh数据层接口。
		/// </summary>
		public static IDAL.IDalZd_PxJh CreateDalZd_PxJh()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_PxJh";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_PxJh)objType;
		}


		/// <summary>
		/// 创建DalZd_PxJl数据层接口。
		/// </summary>
		public static IDAL.IDalZd_PxJl CreateDalZd_PxJl()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_PxJl";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_PxJl)objType;
		}


		/// <summary>
		/// 创建DalZd_SbJc数据层接口。
		/// </summary>
		public static IDAL.IDalZd_SbJc CreateDalZd_SbJc()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_SbJc";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_SbJc)objType;
		}


		/// <summary>
		/// 创建DalZd_Sfys数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Sfys CreateDalZd_Sfys()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Sfys";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Sfys)objType;
		}


		/// <summary>
		/// 创建DalZd_TjJh数据层接口。
		/// </summary>
		public static IDAL.IDalZd_TjJh CreateDalZd_TjJh()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_TjJh";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_TjJh)objType;
		}


		/// <summary>
		/// 创建DalZd_TjJl数据层接口。
		/// </summary>
		public static IDAL.IDalZd_TjJl CreateDalZd_TjJl()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_TjJl";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_TjJl)objType;
		}


		/// <summary>
		/// 创建DalZd_Wsd数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Wsd CreateDalZd_Wsd()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Wsd";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Wsd)objType;
		}


		/// <summary>
		/// 创建DalZd_Xq数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Xq CreateDalZd_Xq()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Xq";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Xq)objType;
		}


		/// <summary>
		/// 创建DalZd_Yd数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yd CreateDalZd_Yd()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yd";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yd)objType;
		}


		/// <summary>
		/// 创建DalZd_Yd_Mdzy数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yd_Mdzy CreateDalZd_Yd_Mdzy()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yd_Mdzy";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yd_Mdzy)objType;
		}


		/// <summary>
		/// 创建DalZd_Yd_Qtry数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yd_Qtry CreateDalZd_Yd_Qtry()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yd_Qtry";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yd_Qtry)objType;
		}


		/// <summary>
		/// 创建DalZd_Yd_Xkzbg数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yd_Xkzbg CreateDalZd_Yd_Xkzbg()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yd_Xkzbg";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yd_Xkzbg)objType;
		}


		/// <summary>
		/// 创建DalZd_Yd_ZlFzr数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yd_ZlFzr CreateDalZd_Yd_ZlFzr()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yd_ZlFzr";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yd_ZlFzr)objType;
		}


		/// <summary>
		/// 创建DalZd_YdJsr数据层接口。
		/// </summary>
		public static IDAL.IDalZd_YdJsr CreateDalZd_YdJsr()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_YdJsr";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_YdJsr)objType;
		}


		/// <summary>
		/// 创建DalZd_Yh数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yh CreateDalZd_Yh()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yh";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yh)objType;
		}


		/// <summary>
		/// 创建DalZd_Ylqx数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Ylqx CreateDalZd_Ylqx()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Ylqx";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Ylqx)objType;
		}


		/// <summary>
		/// 创建DalZd_Yp1数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yp1 CreateDalZd_Yp1()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yp1";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yp1)objType;
		}


		/// <summary>
		/// 创建DalZd_Yp2数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yp2 CreateDalZd_Yp2()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yp2";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yp2)objType;
		}


		/// <summary>
		/// 创建DalZd_Yp2_Ph数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yp2_Ph CreateDalZd_Yp2_Ph()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yp2_Ph";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yp2_Ph)objType;
		}


		/// <summary>
		/// 创建DalZd_Yp2_Ts数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yp2_Ts CreateDalZd_Yp2_Ts()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yp2_Ts";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yp2_Ts)objType;
		}


		/// <summary>
		/// 创建DalZd_Yp3数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yp3 CreateDalZd_Yp3()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yp3";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yp3)objType;
		}


		/// <summary>
		/// 创建DalZd_Yp4数据层接口。
		/// </summary>
		public static IDAL.IDalZd_Yp4 CreateDalZd_Yp4()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_Yp4";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_Yp4)objType;
		}


		/// <summary>
		/// 创建DalZd_ZdYh数据层接口。
		/// </summary>
		public static IDAL.IDalZd_ZdYh CreateDalZd_ZdYh()
		{

			string ClassNamespace = AssemblyPath + ".DalZd_ZdYh";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return (IDAL.IDalZd_ZdYh)objType;
		}
        /// <summary>
        /// 创建DalZd_KjDw数据层接口。
        /// </summary>
        public static IDAL.IDalZd_KjDw CreateDalZd_KjDw()
        {

            string ClassNamespace = AssemblyPath + ".DalZd_KjDw";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalZd_KjDw)objType;
        }

		/// <summary>
		/// 创建DalYk_Rk2数据层接口。
		/// </summary>
		public static  IDAL.IDalYk_Rk2 CreateDalYk_Rk2()
		{

			string ClassNamespace = AssemblyPath + ".DalYk_Rk2";
			object objType = CreateObject(AssemblyPath, ClassNamespace);
			return ( IDAL.IDalYk_Rk2)objType;
		}

	}
}
