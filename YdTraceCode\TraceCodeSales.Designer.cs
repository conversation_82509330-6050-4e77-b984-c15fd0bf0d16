﻿
namespace YdTraceCode
{
    partial class TraceCodeSales
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.myGrid2 = new CustomControl.MyGrid();
            this.myGrid1 = new CustomControl.MyGrid();
            this.c1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.c1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.CmdCopy = new C1.Win.C1Command.C1Command();
            this.c1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.CmdNoTraceCode = new C1.Win.C1Command.C1Command();
            this.c1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.CmdAllNoTrac = new C1.Win.C1Command.C1Command();
            this.c1CommandLink10 = new C1.Win.C1Command.C1CommandLink();
            this.CmdDelete = new C1.Win.C1Command.C1Command();
            this.c1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.c1Command1 = new C1.Win.C1Command.C1CommandControl();
            this.chkAll = new System.Windows.Forms.CheckBox();
            this.TxtTraceCode = new CustomControl.MyTextBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.lblTips = new System.Windows.Forms.Label();
            this.chkCheckRk = new System.Windows.Forms.CheckBox();
            this.chkLength = new System.Windows.Forms.CheckBox();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.tableLayoutPanel1.SuspendLayout();
            this.c1ToolBar1.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 2;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 60F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 40F));
            this.tableLayoutPanel1.Controls.Add(this.myGrid2, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.myGrid1, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.c1ToolBar1, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.TxtTraceCode, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 4;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 33F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(837, 498);
            this.tableLayoutPanel1.TabIndex = 7;
            // 
            // myGrid2
            // 
            this.myGrid2.AllowColMove = true;
            this.myGrid2.AllowFilter = true;
            this.myGrid2.CanCustomCol = false;
            this.myGrid2.Caption = "";
            this.myGrid2.ChildGrid = null;
            this.myGrid2.Col = 0;
            this.myGrid2.ColumnFooters = false;
            this.myGrid2.ColumnHeaders = true;
            this.myGrid2.DataMember = "";
            this.myGrid2.DataSource = null;
            this.myGrid2.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid2.FetchRowStyles = false;
            this.myGrid2.FilterBar = false;
            this.myGrid2.GroupByAreaVisible = true;
            this.myGrid2.Location = new System.Drawing.Point(502, 123);
            this.myGrid2.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid2.Name = "myGrid2";
            this.myGrid2.Size = new System.Drawing.Size(335, 375);
            this.myGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid2.TabIndex = 0;
            this.myGrid2.Xmlpath = null;
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 73);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.tableLayoutPanel1.SetRowSpan(this.myGrid1, 2);
            this.myGrid1.Size = new System.Drawing.Size(502, 425);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 2;
            this.myGrid1.Xmlpath = null;
            this.myGrid1.RowColChange += new C1.Win.C1TrueDBGrid.RowColChangeEventHandler(this.myGrid1_RowColChange);
            this.myGrid1.FetchCellStyle += new C1.Win.C1TrueDBGrid.FetchCellStyleEventHandler(this.myGrid1_FetchCellStyle);
            // 
            // c1ToolBar1
            // 
            this.c1ToolBar1.AccessibleName = "Tool Bar";
            this.c1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.c1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.c1ToolBar1.CommandHolder = null;
            this.c1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.c1CommandLink3,
            this.c1CommandLink4,
            this.c1CommandLink2,
            this.c1CommandLink10,
            this.c1CommandLink1});
            this.c1ToolBar1.Controls.Add(this.chkAll);
            this.c1ToolBar1.Location = new System.Drawing.Point(505, 76);
            this.c1ToolBar1.Name = "c1ToolBar1";
            this.c1ToolBar1.Size = new System.Drawing.Size(314, 44);
            this.c1ToolBar1.Text = "c1ToolBar1";
            this.c1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.c1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // c1CommandLink3
            // 
            this.c1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink3.Command = this.CmdCopy;
            // 
            // CmdCopy
            // 
            this.CmdCopy.Image = global::ZTHisTraceCode.Properties.Resources.复制;
            this.CmdCopy.Name = "CmdCopy";
            this.CmdCopy.ShortcutText = "";
            this.CmdCopy.Text = "复制追溯码";
            this.CmdCopy.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdCopy_Click);
            // 
            // c1CommandLink4
            // 
            this.c1CommandLink4.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink4.Command = this.CmdNoTraceCode;
            this.c1CommandLink4.SortOrder = 1;
            // 
            // CmdNoTraceCode
            // 
            this.CmdNoTraceCode.Image = global::ZTHisTraceCode.Properties.Resources.无码标记;
            this.CmdNoTraceCode.Name = "CmdNoTraceCode";
            this.CmdNoTraceCode.ShortcutText = "";
            this.CmdNoTraceCode.Text = "无码标记";
            this.CmdNoTraceCode.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdNoTraceCode_Click);
            // 
            // c1CommandLink2
            // 
            this.c1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink2.Command = this.CmdAllNoTrac;
            this.c1CommandLink2.SortOrder = 2;
            this.c1CommandLink2.Text = "全部无码";
            // 
            // CmdAllNoTrac
            // 
            this.CmdAllNoTrac.Image = global::ZTHisTraceCode.Properties.Resources.无码标记;
            this.CmdAllNoTrac.Name = "CmdAllNoTrac";
            this.CmdAllNoTrac.ShortcutText = "";
            this.CmdAllNoTrac.Text = "全部无码";
            this.CmdAllNoTrac.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdAllNoTrac_Click);
            // 
            // c1CommandLink10
            // 
            this.c1CommandLink10.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink10.Command = this.CmdDelete;
            this.c1CommandLink10.SortOrder = 3;
            // 
            // CmdDelete
            // 
            this.CmdDelete.Image = global::ZTHisTraceCode.Properties.Resources.删除;
            this.CmdDelete.Name = "CmdDelete";
            this.CmdDelete.ShortcutText = "";
            this.CmdDelete.Text = "删除";
            this.CmdDelete.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdDelete_Click);
            // 
            // c1CommandLink1
            // 
            this.c1CommandLink1.Command = this.c1Command1;
            this.c1CommandLink1.SortOrder = 4;
            this.c1CommandLink1.Text = "新命令";
            // 
            // c1Command1
            // 
            this.c1Command1.Control = this.chkAll;
            this.c1Command1.Name = "c1Command1";
            this.c1Command1.ShortcutText = "";
            this.c1Command1.Text = "新命令";
            // 
            // chkAll
            // 
            this.chkAll.AutoSize = true;
            this.chkAll.BackColor = System.Drawing.Color.Transparent;
            this.chkAll.Location = new System.Drawing.Point(259, 13);
            this.chkAll.Name = "chkAll";
            this.chkAll.Size = new System.Drawing.Size(54, 18);
            this.chkAll.TabIndex = 8;
            this.chkAll.Text = "全选";
            this.chkAll.UseVisualStyleBackColor = false;
            this.chkAll.CheckedChanged += new System.EventHandler(this.chkAll_CheckedChanged);
            // 
            // TxtTraceCode
            // 
            this.TxtTraceCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtTraceCode.Captain = "扫描追溯码";
            this.TxtTraceCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtTraceCode.CaptainFont = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtTraceCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtTraceCode.CaptainWidth = 70F;
            this.tableLayoutPanel1.SetColumnSpan(this.TxtTraceCode, 2);
            this.TxtTraceCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtTraceCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtTraceCode.EditMask = null;
            this.TxtTraceCode.EnterToTab = false;
            this.TxtTraceCode.Location = new System.Drawing.Point(3, 36);
            this.TxtTraceCode.Multiline = false;
            this.TxtTraceCode.Name = "TxtTraceCode";
            this.TxtTraceCode.PasswordChar = '\0';
            this.TxtTraceCode.ReadOnly = false;
            this.TxtTraceCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtTraceCode.SelectionStart = 0;
            this.TxtTraceCode.SelectStart = 0;
            this.TxtTraceCode.Size = new System.Drawing.Size(831, 34);
            this.TxtTraceCode.TabIndex = 1;
            this.TxtTraceCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtTraceCode.TextFont = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtTraceCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtTraceCode.Watermark = null;
            this.TxtTraceCode.KeyDown += new System.Windows.Forms.KeyEventHandler(this.TxtTraceCode_KeyDown);
            // 
            // panel1
            // 
            this.tableLayoutPanel1.SetColumnSpan(this.panel1, 2);
            this.panel1.Controls.Add(this.lblTips);
            this.panel1.Controls.Add(this.chkCheckRk);
            this.panel1.Controls.Add(this.chkLength);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Margin = new System.Windows.Forms.Padding(0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(837, 33);
            this.panel1.TabIndex = 4;
            // 
            // lblTips
            // 
            this.lblTips.AutoSize = true;
            this.lblTips.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTips.ForeColor = System.Drawing.Color.Red;
            this.lblTips.Location = new System.Drawing.Point(396, 4);
            this.lblTips.Name = "lblTips";
            this.lblTips.Size = new System.Drawing.Size(362, 21);
            this.lblTips.TabIndex = 2;
            this.lblTips.Text = "勾选出库追溯码检测入库，扫码无需按药品顺序扫";
            // 
            // chkCheckRk
            // 
            this.chkCheckRk.AutoSize = true;
            this.chkCheckRk.Location = new System.Drawing.Point(188, 7);
            this.chkCheckRk.Name = "chkCheckRk";
            this.chkCheckRk.Size = new System.Drawing.Size(180, 18);
            this.chkCheckRk.TabIndex = 1;
            this.chkCheckRk.Text = "出库追溯码检测是否入库";
            this.chkCheckRk.UseVisualStyleBackColor = true;
            this.chkCheckRk.CheckedChanged += new System.EventHandler(this.chkCheckRk_CheckedChanged);
            // 
            // chkLength
            // 
            this.chkLength.AutoSize = true;
            this.chkLength.Location = new System.Drawing.Point(12, 7);
            this.chkLength.Name = "chkLength";
            this.chkLength.Size = new System.Drawing.Size(152, 18);
            this.chkLength.TabIndex = 0;
            this.chkLength.Text = "检查追溯码20位长度";
            this.chkLength.UseVisualStyleBackColor = true;
            this.chkLength.CheckedChanged += new System.EventHandler(this.chkLength_CheckedChanged);
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.CmdNoTraceCode);
            this.c1CommandHolder1.Commands.Add(this.CmdDelete);
            this.c1CommandHolder1.Commands.Add(this.c1Command1);
            this.c1CommandHolder1.Commands.Add(this.CmdAllNoTrac);
            this.c1CommandHolder1.Commands.Add(this.CmdCopy);
            this.c1CommandHolder1.Owner = this;
            // 
            // TraceCodeSales
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(837, 498);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "TraceCodeSales";
            this.Text = "药品追溯码录入";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.TraceCodeSales_FormClosed);
            this.Load += new System.EventHandler(this.TraceCodeDetail_Load);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.c1ToolBar1.ResumeLayout(false);
            this.c1ToolBar1.PerformLayout();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private CustomControl.MyGrid myGrid1;
        private C1.Win.C1Command.C1ToolBar c1ToolBar1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink4;
        private C1.Win.C1Command.C1Command CmdNoTraceCode;
        private C1.Win.C1Command.C1CommandLink c1CommandLink10;
        private C1.Win.C1Command.C1Command CmdDelete;
        private C1.Win.C1Command.C1CommandLink c1CommandLink1;
        private C1.Win.C1Command.C1CommandControl c1Command1;
        private System.Windows.Forms.CheckBox chkAll;
        private CustomControl.MyGrid myGrid2;
        private CustomControl.MyTextBox TxtTraceCode;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink2;
        private C1.Win.C1Command.C1Command CmdAllNoTrac;
        private C1.Win.C1Command.C1CommandLink c1CommandLink3;
        private C1.Win.C1Command.C1Command CmdCopy;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.CheckBox chkLength;
        private System.Windows.Forms.CheckBox chkCheckRk;
        private System.Windows.Forms.Label lblTips;
    }
}