-- 数据库更新脚本 - 20250803
-- 功能：添加 Yk_Ck1.Ck_Ok 字段，创建 Yk_Ck2 和 Yk_CkDrugtracinfo 表，添加外键关系

-- OSQL.EXE 数据版本
SET NOCOUNT ON
GO

BEGIN TRANSACTION
GO

IF COL_LENGTH('dbo.Yk_Ck1', 'Ck_Ok') IS NULL
BEGIN
	ALTER TABLE dbo.Yk_Ck1 ADD Ck_Ok VARCHAR(50) DEFAULT '未结算'
	PRINT '已成功添加 Yk_Ck1.Ck_Ok 列'
END
ELSE
BEGIN
	PRINT 'Yk_Ck1.Ck_Ok 列已存在，跳过创建'
END
GO


-- 创建 Yk_Ck2 表
IF OBJECT_ID('dbo.Yk_Ck2', 'U') IS NULL
BEGIN
	CREATE TABLE [dbo].[Yk_Ck2]
	(
		[Ck_Id] [int] NOT NULL,
		[Ck_Code] [char](9) NOT NULL,
		[Yp_Code] [char](11) NULL,
		[Ck_Sl] [numeric](12, 4) NULL,
		[Ck_Dj] [numeric](18, 6) NULL,
		[Ck_Money] [numeric](18, 4) NULL,
		[Ck_Memo] [varchar](50) NULL,
		CONSTRAINT [PK_Yk_Ck2_1] PRIMARY KEY CLUSTERED
    (
        [Ck_Id] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]

	PRINT '已成功创建 Yk_Ck2 表'
END
ELSE
BEGIN
	PRINT 'Yk_Ck2 表已存在，跳过创建'
END
GO
-- 创建 Yk_CkDrugtracinfo 表
IF OBJECT_ID('dbo.Yk_CkDrugtracinfo', 'U') IS NULL
BEGIN
	CREATE TABLE [dbo].[Yk_CkDrugtracinfo]
	(
		[Ck_Id] [int] NOT NULL,
		[Ck_Code] [char](9) NOT NULL,
		[drug_trac_codg] [varchar](100) NOT NULL,
		CONSTRAINT [PK_Yk_CkDrugtracinfo] PRIMARY KEY CLUSTERED
    (
        [Ck_Id] ASC,
        [drug_trac_codg] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]

	PRINT '已成功创建 Yk_CkDrugtracinfo 表'
END
ELSE
BEGIN
	PRINT 'Yk_CkDrugtracinfo 表已存在，跳过创建'
END
GO
-- 添加 Yk_Ck2 表的默认约束
IF OBJECT_ID('dbo.Yk_Ck2', 'U') IS NOT NULL
BEGIN
	IF NOT EXISTS (SELECT 1
	FROM sys.default_constraints
	WHERE name = 'DF_Yk_Ck2_Ck_Sl')
        ALTER TABLE [dbo].[Yk_Ck2] ADD CONSTRAINT [DF_Yk_Ck2_Ck_Sl] DEFAULT ((0)) FOR [Ck_Sl]

	IF NOT EXISTS (SELECT 1
	FROM sys.default_constraints
	WHERE name = 'DF_Yk_Ck2_Ck_Dj')
        ALTER TABLE [dbo].[Yk_Ck2] ADD CONSTRAINT [DF_Yk_Ck2_Ck_Dj] DEFAULT ((0)) FOR [Ck_Dj]

	IF NOT EXISTS (SELECT 1
	FROM sys.default_constraints
	WHERE name = 'DF_Yk_Ck2_Ck_Money')
        ALTER TABLE [dbo].[Yk_Ck2] ADD CONSTRAINT [DF_Yk_Ck2_Ck_Money] DEFAULT ((0)) FOR [Ck_Money]
END
GO
-- 添加 Yk_Ck2 表的列注释
IF OBJECT_ID('dbo.Yk_Ck2', 'U') IS NOT NULL
BEGIN
	IF EXISTS (SELECT 1
	FROM sys.columns
	WHERE object_id = OBJECT_ID('dbo.Yk_Ck2') AND name = 'Ck_Sl')
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房出库_数量', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Sl'

	IF EXISTS (SELECT 1
	FROM sys.columns
	WHERE object_id = OBJECT_ID('dbo.Yk_Ck2') AND name = 'Ck_Dj')
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房出库_单价', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Dj'

	IF EXISTS (SELECT 1
	FROM sys.columns
	WHERE object_id = OBJECT_ID('dbo.Yk_Ck2') AND name = 'Ck_Money')
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房出库_金额', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Money'

	IF EXISTS (SELECT 1
	FROM sys.columns
	WHERE object_id = OBJECT_ID('dbo.Yk_Ck2') AND name = 'Ck_Memo')
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房出库_备注', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Ck2', @level2type=N'COLUMN',@level2name=N'Ck_Memo'
END
GO

-- 添加外键关系
-- Yk_Ck2 和 Yk_Ck1 的外键关系（通过 Ck_Code）
IF NOT EXISTS (SELECT 1
FROM sys.foreign_keys
WHERE name = 'FK_Yk_Ck2_Yk_Ck1')
BEGIN
	ALTER TABLE [dbo].[Yk_Ck2] ADD CONSTRAINT [FK_Yk_Ck2_Yk_Ck1] FOREIGN KEY([Ck_Code]) REFERENCES [dbo].[Yk_Ck1] ([Ck_Code])
	PRINT '已成功添加 Yk_Ck2 和 Yk_Ck1 的外键关系'
END
ELSE
BEGIN
	PRINT 'Yk_Ck2 和 Yk_Ck1 的外键关系已存在，跳过创建'
END
GO

-- Yk_CkDrugtracinfo 和 Yk_Ck2 的外键关系（通过 Ck_Id）
IF NOT EXISTS (SELECT 1
FROM sys.foreign_keys
WHERE name = 'FK_Yk_CkDrugtracinfo_Yk_Ck2')
BEGIN
	ALTER TABLE [dbo].[Yk_CkDrugtracinfo] ADD CONSTRAINT [FK_Yk_CkDrugtracinfo_Yk_Ck2] FOREIGN KEY([Ck_Id]) REFERENCES [dbo].[Yk_Ck2] ([Ck_Id])
	PRINT '已成功添加 Yk_CkDrugtracinfo 和 Yk_Ck2 的外键关系'
END
ELSE
BEGIN
	PRINT 'Yk_CkDrugtracinfo 和 Yk_Ck2 的外键关系已存在，跳过创建'
END
GO

-- Yk_CkDrugtracinfo 和 Yk_Ck1 的外键关系（通过 Ck_Code）
IF NOT EXISTS (SELECT 1
FROM sys.foreign_keys
WHERE name = 'FK_Yk_CkDrugtracinfo_Yk_Ck1')
BEGIN
	ALTER TABLE [dbo].[Yk_CkDrugtracinfo] ADD CONSTRAINT [FK_Yk_CkDrugtracinfo_Yk_Ck1] FOREIGN KEY([Ck_Code]) REFERENCES [dbo].[Yk_Ck1] ([Ck_Code])
	PRINT '已成功添加 Yk_CkDrugtracinfo 和 Yk_Ck1 的外键关系'
END
ELSE
BEGIN
	PRINT 'Yk_CkDrugtracinfo 和 Yk_Ck1 的外键关系已存在，跳过创建'
END
GO

COMMIT TRANSACTION
PRINT '数据库更新成功完成！'
GO
