using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using YdPublicFunction;
using YdResources;

namespace YdBusiness
{
    public partial class Dd_Order1 : Common.BaseForm.BaseDict1
    {
        private BLL.BllDd1 _bllDd1 = new BllDd1();
        public Dd_Order1()
        {
            InitializeComponent();
        }

        private void Dd_Order1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            FormInit();
            DataInit();
            myGrid1.Select();
        }
        #region 初始化
        private void FormInit()
        {
            doubleDateEdit1.CustomFormat = "yyyy-MM-dd";
            doubleDateEdit1.DisplayFormat = "yyyy-MM-dd";
            doubleDateEdit1.EditFormat = "yyyy-MM-dd";
            doubleDateEdit1.SelectedIndex = 5;

            myGrid1.Init_Grid();
            myGrid1.Init_Column("验收状态", "Dd_Finish", 80, "中", "", false);
            myGrid1.Init_Column("订单编码", "Dd_Code", 120, "中", "", false);
            myGrid1.Init_Column("订单日期", "Dd_Date", 120, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("供应商编码", "Kh_Code", 100, "中", "", false);
            myGrid1.Init_Column("供应商名称", "Kh_Name", 200, "左", "", false);
            myGrid1.Init_Column("订单金额", "Dd_Money", 120, "右", "#,##0.00", false);
            myGrid1.Init_Column("采购员", "Cgy_Name", 100, "左", "", false);
            myGrid1.Init_Column("验收员", "Ysy_Name", 100, "左", "", false);
            myGrid1.Init_Column("验收日期", "Ys_Date", 120, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("备注", "Dd_Memo", 200, "左", "", false);
            myGrid1.Splits[0].DisplayColumns["Dd_Finish"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsSh_FetchCellStyle;
            myGrid1.ColumnFooters = true;
            myGrid1.AllowSort = true;
        }
        #endregion

        #region 自定义函数
        private void DataInit()
        {
            base.MyTable = _bllDd1.GetList($"Dd_Date between '{DateTime.Parse(doubleDateEdit1.StartValue.ToString()).ToString("yyy-MM-dd")}' " +
                                         $"And '{DateTime.Parse(doubleDateEdit1.EndValue.ToString()).ToString("yyy-MM-dd 23:59:59")}'").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Dd_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            myGrid1.BeginInvoke(new Action(() => this.myGrid1.DataTable = base.MyTable));
            base.MyView = (DataView)base.MyCm.List;
            base.MyView.Sort = "Dd_Date desc";
            DataSum();
        }
        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            Dd_Order2 vform = new Dd_Order2(base.Insert, base.MyRow, base.MyTable);
            vform.Tag = base.MyRow["Dd_Code"];
            vform.ZbTransmitTxt = base.MyTransmitTxt;
            base.AddTabControl(vform, "采购订单明细-" + (base.MyRow["Kh_Name"].ToString() == "" ? "新订单" : base.MyRow["Kh_Name"].ToString()), YdResources.C_Resources.GetImage16(""));
        }
        protected override void DataDelete()
        {
            if (myGrid1.Row + 1 > myGrid1.RowCount)
            {
                MessageBox.Show("请选择采购订单记录!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;

            if (MessageBox.Show("是否删除:采购订单【" + base.MyRow["Kh_Name"] + "】记录?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No) return;

            if (_bllDd1.Delete(base.MyRow["Dd_Code"].ToString()) == true)
            {
                myGrid1.Delete();
                base.MyTable.AcceptChanges();
                DataSum();
                MessageBox.Show("数据删除成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
        }

        private void DataSum()
        {
            LblTotal.BeginInvoke(new Action(() => this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString()));
        }
        #endregion

        #region 事件
        private void Cmd_Add_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEdit(true);
        }
        private void Cmd_Del_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataDelete();
        }
        private void CmdQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataInit();
        }
        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            char[] split = { ' ' };
            string filter = "Kh_Name+Kh_Code+Cgy_Name+Ysy_Name";
            string strFilter = "";
            foreach (string substr in TxtFilter.Text.Replace("*", "[*]").Replace("%", "[%]").Split(split))
            {
                strFilter = strFilter + filter + " like '*" + substr + "*' And ";
            }
            strFilter = strFilter.Substring(0, strFilter.Length - 5);
            MyView.RowFilter = strFilter;
            DataSum();
        }
        #endregion
    }
}
