﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlYk_RkDrugtracinfo.cs
*
* 功 能： N/A
* 类 名： MdlYk_RkDrugtracinfo
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/8/1 10:30:44   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace  Model
{
	/// <summary>
	/// MdlYk_RkDrugtracinfo:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlYk_RkDrugtracinfo
	{
		public MdlYk_RkDrugtracinfo()
		{}
		#region Model
		private int _rk_id;
		private string _drug_trac_codg;
		private string _rk_code;
		/// <summary>
		/// 
		/// </summary>
		public int Rk_Id
		{
			set{ _rk_id=value;}
			get{return _rk_id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string drug_trac_codg
		{
			set{ _drug_trac_codg=value;}
			get{return _drug_trac_codg;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Rk_Code
		{
			set{ _rk_code=value;}
			get{return _rk_code;}
		}
		#endregion Model

	}
}

