﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYk_Rk1.cs
*
* 功 能： N/A
* 类 名： DalYk_Rk1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
using System.Collections.Generic;

namespace SQLServerDAL
{
    /// <summary>
    /// 数据访问类:DalYk_Rk1
    /// </summary>
    public partial class DalYk_Rk1 : IDalYk_Rk1
    {
        public DalYk_Rk1()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string Rk_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Yk_Rk1");
            strSql.Append(" where Rk_Code=@Rk_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Code", SqlDbType.Char,9)          };
            parameters[0].Value = Rk_Code;

            return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.MdlYk_Rk1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Yk_Rk1(");
            strSql.Append("Rk_Date,Rk_Code,Czy_Code,Czy_Name,Rk_Memo,Sc_Finish,Kh_Code,Kh_Name,Rk_Ok)");
            strSql.Append(" values (");
            strSql.Append("@Rk_Date,@Rk_Code,@Czy_Code,@Czy_Name,@Rk_Memo,@Sc_Finish,@Kh_Code,@Kh_Name,@Rk_Ok)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Rk_Code", SqlDbType.Char,9),
                    new SqlParameter("@Czy_Code", SqlDbType.Char,4),
                    new SqlParameter("@Czy_Name", SqlDbType.VarChar,10),
                    new SqlParameter("@Rk_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
                    new SqlParameter("@Kh_Code", SqlDbType.Char,4),
                    new SqlParameter("@Kh_Name", SqlDbType.VarChar,100),
                    new SqlParameter("@Rk_Ok", SqlDbType.VarChar,50)};
            parameters[0].Value = model.Rk_Date;
            parameters[1].Value = model.Rk_Code;
            parameters[2].Value = model.Czy_Code;
            parameters[3].Value = model.Czy_Name;
            parameters[4].Value = model.Rk_Memo;
            parameters[5].Value = model.Sc_Finish;
            parameters[6].Value = model.Kh_Code;
            parameters[7].Value = model.Kh_Name;
            parameters[8].Value = model.Rk_Ok;
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.MdlYk_Rk1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Yk_Rk1 set ");
            strSql.Append("Rk_Date=@Rk_Date,");
            strSql.Append("Czy_Code=@Czy_Code,");
            strSql.Append("Czy_Name=@Czy_Name,");
            strSql.Append("Rk_Memo=@Rk_Memo,");
            strSql.Append("Sc_Finish=@Sc_Finish,");
            strSql.Append("Kh_Code=@Kh_Code,");
            strSql.Append("Kh_Name=@Kh_Name,");
            strSql.Append("Rk_Ok=@Rk_Ok");
            strSql.Append(" where Rk_Code=@Rk_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Czy_Code", SqlDbType.Char,4),
                    new SqlParameter("@Czy_Name", SqlDbType.VarChar,10),
                    new SqlParameter("@Rk_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
                    new SqlParameter("@Kh_Code", SqlDbType.Char,4),
                    new SqlParameter("@Kh_Name", SqlDbType.VarChar,100),
                    new SqlParameter("@Rk_Ok", SqlDbType.VarChar,50),
                    new SqlParameter("@Rk_Code", SqlDbType.Char,9)};
            parameters[0].Value = model.Rk_Date;
            parameters[1].Value = model.Czy_Code;
            parameters[2].Value = model.Czy_Name;
            parameters[3].Value = model.Rk_Memo;
            parameters[4].Value = model.Sc_Finish;
            parameters[5].Value = model.Kh_Code;
            parameters[6].Value = model.Kh_Name;
            parameters[7].Value = model.Rk_Ok;
            parameters[8].Value = model.Rk_Code;

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string Rk_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Yk_Rk1 ");
            strSql.Append(" where Rk_Code=@Rk_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Code", SqlDbType.Char,9)          };
            parameters[0].Value = Rk_Code;

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Rk_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Yk_Rk1 ");
            strSql.Append(" where Rk_Code in (" + Rk_Codelist + ")  ");
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MdlYk_Rk1 GetModel(string Rk_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Rk_Date,Rk_Code,Czy_Code,Czy_Name,Rk_Memo,Sc_Finish,Kh_Code,Kh_Name,Rk_Ok from Yk_Rk1 ");
            strSql.Append(" where Rk_Code=@Rk_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Code", SqlDbType.Char,9)          };
            parameters[0].Value = Rk_Code;

            Model.MdlYk_Rk1 model = new Model.MdlYk_Rk1();
            DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MdlYk_Rk1 DataRowToModel(DataRow row)
        {
            Model.MdlYk_Rk1 model = new Model.MdlYk_Rk1();
            if (row != null)
            {
                if (row["Rk_Date"] != null && row["Rk_Date"].ToString() != "")
                {
                    model.Rk_Date = DateTime.Parse(row["Rk_Date"].ToString());
                }
                if (row["Rk_Code"] != null)
                {
                    model.Rk_Code = row["Rk_Code"].ToString();
                }
                if (row["Czy_Code"] != null)
                {
                    model.Czy_Code = row["Czy_Code"].ToString();
                }
                if (row["Czy_Name"] != null)
                {
                    model.Czy_Name = row["Czy_Name"].ToString();
                }
                if (row["Rk_Memo"] != null)
                {
                    model.Rk_Memo = row["Rk_Memo"].ToString();
                }
                if (row["Sc_Finish"] != null && row["Sc_Finish"].ToString() != "")
                {
                    if ((row["Sc_Finish"].ToString() == "1") || (row["Sc_Finish"].ToString().ToLower() == "true"))
                    {
                        model.Sc_Finish = true;
                    }
                    else
                    {
                        model.Sc_Finish = false;
                    }
                }
                if (row["Kh_Code"] != null)
                {
                    model.Kh_Code = row["Kh_Code"].ToString();
                }
                if (row["Kh_Name"] != null)
                {
                    model.Kh_Name = row["Kh_Name"].ToString();
                }
                if (row["Rk_Ok"] != null)
                {
                    model.Rk_Ok = row["Rk_Ok"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Rk_Code,Rk_Date,Kh_Code,Kh_Name,Rk_Memo,Rk_Money,Yk_Rk1.Czy_Code,Rk_Ok,Zd_Czy.Czy_Name  ");
            strSql.Append(" FROM Yk_Rk1 ");
            strSql.Append(" INNER JOIN Zd_Czy ON Zd_Czy.Czy_Code = Yk_Rk1.Czy_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Rk_Date,Rk_Code,Czy_Code,Czy_Name,Rk_Memo,Sc_Finish,Kh_Code,Kh_Name,Rk_Ok ");
            strSql.Append(" FROM Yk_Rk1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Yk_Rk1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Rk_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Yk_Rk1 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yk_Rk1";
			parameters[1].Value = "Rk_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        public string GetWwc(string Jsr_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT STUFF( ");
            strSql.Append("    (SELECT DISTINCT char(10) + CONVERT(VARCHAR(10), Rk_Date, 120) ");
            strSql.Append($"   FROM Yk_Rk1 WHERE Rk_Ok = '未完成' And Czy_Code='{Jsr_Code}'");
            strSql.Append("    FOR XML PATH('')),  ");
            strSql.Append("    1, 1, '') AS DateList; ");
            return Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString()) + "";
        }

        /// <summary>
        /// 暂时先不考虑Zd_Yp3
        /// </summary>
        /// <param name="Rk_Code"></param>
        /// <returns></returns>
        //public bool DeleteAll(string Rk_Code)
        //{
        //    List<string> strList = new List<string>();
        //    List<IDataParameter[]> parametersList = new List<IDataParameter[]>();

        //    StringBuilder strSql3 = new StringBuilder();
        //    strSql3.Append("delete from Yk_RkDrugtracinfo ");
        //    strSql3.Append(" where Rk_Code=@Rk_Code");
        //    SqlParameter[] parameters3 = {
        //        new SqlParameter("@Rk_Code", SqlDbType.Char,9)};
        //    parameters3[0].Value = Rk_Code;

        //    strList.Add(strSql3.ToString());
        //    parametersList.Add(parameters3);

        //    StringBuilder strSql1 = new StringBuilder();
        //    strSql1.Append("delete from Yk_Rk2 ");
        //    strSql1.Append(" where Rk_Code=@Rk_Code");
        //    SqlParameter[] parameters1 = {
        //        new SqlParameter("@Rk_Code", SqlDbType.Char,9)};
        //    parameters1[0].Value = Rk_Code;

        //    strList.Add(strSql1.ToString());
        //    parametersList.Add(parameters1);

        //    StringBuilder strSql = new StringBuilder();
        //    strSql.Append("delete from Yk_Rk1 ");
        //    strSql.Append(" where Rk_Code=@Rk_Code");
        //    SqlParameter[] parameters = {
        //            new SqlParameter("@Rk_Code", SqlDbType.Char,9)};
        //    parameters[0].Value = Rk_Code;

        //    strList.Add(strSql.ToString());
        //    parametersList.Add(parameters);


        //}


        #endregion  ExtensionMethod
    }
}

